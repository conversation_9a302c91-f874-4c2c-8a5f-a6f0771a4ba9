<template>
  <div class="card-img">
    <img :src="getImgUrl()" alt="">
  </div>
</template>

<script>
  export default {
    name: 'CardImg',
    data () {
      return {
        imgConfigData:{
          "tag": "img",
          "img_key": "img_v3_0238_073f1823-df2b-4377-86c6-e293f18abcef", // 图片的 Key。可通过上传图片接口或在搭建工具中上传图片后获得。
          "alt": {
            // 光标悬浮（hover）在图片上时展示的说明。
            "tag": "plain_text",
            "content": ""
          },
          "corner_radius": "5", // 多图混排图片的圆角半径，单位是像素（px）。
          //"scale_type": "crop_top", // 图片的裁剪模式，当 size 字段的比例和图片的比例不一致时会触发裁剪。
          "size": "100|100", // 图片尺寸
          "transparent": false, // 是否为透明底色。默认为 false，即图片为白色底色。
          "preview": false, // 点击后是否放大图片。默认值为 true。
          //"mode": "large", // 图片尺寸模式。
          //"custom_width": 500, // 自定义图片的最大展示宽度。
          //"compact_width": false // 是否展示为紧凑型的图片。
        }
      }
    },
    methods: {
      getImgUrl () {
        //TODO this.imgConfigData.img_key 下载文件的接口fid，新的那套文件下载服务
        return 'https://img1.baidu.com/it/u=1667670760,1667670760&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500';
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>