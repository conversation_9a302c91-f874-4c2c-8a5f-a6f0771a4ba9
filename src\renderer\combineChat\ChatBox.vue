<template>
  <div class="view-scroll-wrap">
    <ViewScroll ref="container_view"
                class="chatscroll-container container_view"
                :scrollList="viewChatList"
                :isInRightContent="true"
                @scroll="scroll"
                @scrollHandle="scrollHandle">
      <template v-slot:content="chatItemProp">
        <section class="chatmessage-container">
          <section>
            <component :is="'dd'"
                       :id="chatItemProp.chatItem.uuid"
                       v-bind="chatTypeComputed(chatItemProp.chatItem, false)"
                       v-time-change="{chatItem:chatItemProp.chatItem.timestamp}"
                       :ts="chatItemProp.chatItem.timestamp"
                       class="menu"
                       @rightClickMessage="openRightPanel"
                       :ref="chatItemProp.chatItem.uuid"></component>
          </section>
        </section>
      </template>
    </ViewScroll>
    <RightClickOption :tranLeft="tranLeft"
                      :tranTop="tranTop"
                      :tranRight="tranRight"
                      :show="chatContextMenuVisible"
                      :options="rightPanelOptions"
                      :chatInfo="currentChatInfo"
                      ref="rightClickOption" />
  </div>
</template>

<script>
import Bus from '@/utils/vueBus';
import ViewScroll from '@/components/ViewScroll';
import {mapActions, mapGetters} from 'vuex';
import rightOptionsMix from '@/mixins/rightOptionsMix';
import chatTranslateMix from '@/mixins/chatTranslateMix';
import {dataUtil} from '@/utils/dataUtil';
import {getTimestamp} from '@/utils/timestampFix';
import PanelSplit from '@/components/common/PanelSplit';
import RightClickOption from '@/components/Panel/RightClickOption';
import TimeChat from '@/components/TimeChat.vue';
import E2ETips from '@/components/Chat/E2ETips.vue';
import E2EKeyChangeTip from '@/components/Chat/E2EKeyChangeTip.vue';
import E2EDescript from '@/components/Chat/E2EDescript.vue';
import NameCard from '@/components/Chat/NameCard.vue';
import VoiceChat from '@/components/Chat/VoiceChat/VoiceChat.vue';
import PictureChat from '@/components/Chat/PictureChat.vue';
import MessageTxtChat from '@/components/Chat/MessageTxtChat.vue';
import AnnouncementChat from '@/components/Chat/AnnouncementChat.vue';
import CombineChat from '@/components/Chat/CombineChat.vue';
import MeetingInviteChat from '@/components/Chat/MeetingInviteChat.vue';
import RobotRichChat from '@/components/Chat/RobotRichChat.vue';
import ForRobotRichChat from '@/components/Chat/ForRobotRichChat.vue';
import MessageTxtRichChat from '@/components/Chat/MessageTxtRichChat.jsx';
import DocumentChat from '@/components/Chat/DocumentChat.vue';
import CallRecordChat from '@/components/Chat/CallRecordChat.vue';
import MessageCard from '@/components/Chat/MessageCards/MessageCard.vue';
import MeetingCard from '@/components/Chat/MeetingCard.vue';
import ApprovalCard from '@/components/Chat/ApprovalCard.vue';
import ApprovalForwardCard from '@/components/Chat/ApprovalForwardCard.vue';
import UnknowChat from '@/components/Chat/UnknowChat.vue';
import UnsupportedChat from '@/components/Chat/UnsupportedChat.vue';
import MapView from '@/components/Chat/MapView.vue';
import MessageComponents from '@/components/Chat/MessageComponents/index.vue';
import timeChange from '@/directive/time-change';
import * as Caught from '@/utils/caughtError';
import {existFile} from '@/utils/FileTool';
import {setClipboard} from '@/utils/clipboard.js';
import {ipcRenderer} from 'electron';
import * as Tool from '@/utils/FileTool';
import {msgRestrictedTypeInfo} from '@/utils/renderMsgItem';
import {parseMarkdownToPlainText} from '@/utils/editor/parse.js';
import QueueManager from '@/utils/QueueManager';
import {defaultSpaceId} from '@/dataController/hid';

const CHAT_TYPE = {
    VCARD: 'NameCard',
    VOICE: 'VoiceChat',
    MESSAGE: 'MessageTxtRichChat',
    MESSAGELONG: 'MessageTxtRichChat',
    UNKNOW: 'UnknowChat',
    UNSUPPORTED: 'UnsupportedChat',
    RESTRICTED: 'MessageTxtChat',
    E2ETIPS: 'E2ETips',
    E2EKeyChangeTip: 'E2EKeyChangeTip',
    TIME: 'TimeChat',
    // EVENT: 'EventChat',
    PICTURE: 'PictureChat',
    STICKER: 'StickerChat',
    GIF: 'GifChat',
    DOCUMENT: 'DocumentChat',
    RICHURL: 'RichUrlChat',
    MEETINGINVITE: 'MeetingInviteChat',
    Announcement: 'AnnouncementChat',
    CombineText: 'CombineChat',
    RobotText: 'MessageTxtChat',
    RobotRich: 'RobotRichChat',
    ForRobotRich: 'ForRobotRichChat',
    CALLRECORD: 'CallRecordChat',
    E2EDESCRIPT: 'E2EDescript',
    MSGCARD: 'MessageCard',
    MEETINGCARD: 'MeetingCard',
    APPROVALCARD: 'ApprovalCard',
    APPROVALFORWARDCARD: 'ApprovalForwardCard',
    MAPVIEW: 'MapView',
    RICHTEXT: 'MessageComponents'
};
export default {
    name: 'ChatBox',
    mixins: [rightOptionsMix, chatTranslateMix],
    directives: {
        timeChange: timeChange
    },
    components: {
        // eslint-disable-next-line vue/no-unused-components
        E2ETips,
        // eslint-disable-next-line vue/no-unused-components
        E2EKeyChangeTip,
        // eslint-disable-next-line vue/no-unused-components
        E2EDescript,
        // eslint-disable-next-line vue/no-unused-components
        NameCard,
        // eslint-disable-next-line vue/no-unused-components
        VoiceChat,
        // eslint-disable-next-line vue/no-unused-components
        MessageTxtChat,
        // eslint-disable-next-line vue/no-unused-components
        AnnouncementChat,
        // eslint-disable-next-line vue/no-unused-components
        CombineChat,
        // eslint-disable-next-line vue/no-unused-components
        RobotRichChat,
        // eslint-disable-next-line vue/no-unused-components
        ForRobotRichChat,
        // eslint-disable-next-line vue/no-unused-components
        PictureChat,
        // eslint-disable-next-line vue/no-unused-components
        ViewScroll,
        // eslint-disable-next-line vue/no-unused-components
        TimeChat,
        // eslint-disable-next-line vue/no-unused-components
        DocumentChat,
        // eslint-disable-next-line vue/no-unused-components
        MapView,
        // eslint-disable-next-line vue/no-unused-components
        MeetingInviteChat,
        // eslint-disable-next-line vue/no-unused-components
        CallRecordChat,
        // eslint-disable-next-line vue/no-unused-components
        PanelSplit,
        // eslint-disable-next-line vue/no-unused-components
        MessageCard,
        // eslint-disable-next-line vue/no-unused-components
        MeetingCard,
        // eslint-disable-next-line vue/no-unused-components
        ApprovalCard,
        // eslint-disable-next-line vue/no-unused-components
        ApprovalForwardCard,
        // eslint-disable-next-line vue/no-unused-components
        MessageComponents,
        // eslint-disable-next-line vue/no-unused-components
        UnknowChat,
        // eslint-disable-next-line vue/no-unused-components
        UnsupportedChat,
        // eslint-disable-next-line vue/no-unused-components
        MessageTxtRichChat,
        // eslint-disable-next-line vue/no-unused-components
        RightClickOption
    },
    props: {
        viewChatList: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            tranLeft: 0,
            tranTop: 0,
            tranRight: 'auto',
            rightPanelOptions: [],
            currentChatInfo: {},
            queueManager: {push: () => Promise.resolve}, // 转发消息管理
            currentDialogReplyInfo: {
                replyPannelVisible: false,
                replyChatInfo: {}
            },
            viewChatLists: [
                {
                    customData: {
                        filename: '60e53fe3c870e8c29f3f2e13720b9dd7.jpg',
                        width: 1080,
                        height: 1621,
                        url: 'https://tech01-mapi.svc.matrx.tech/pfm/download/file?fid=6859kbl1n10f0321c1740001aa2b09&ts=1750667010817&id=uZl7pwwjoQyXb%5FGq&os=desktop'
                    },
                    direct: 'LEFT',
                    token: 'AVk4Q4ELBF4',
                    chatComponentType: 'PICTURE',
                    timestamp: 1750821025531,
                    uuid: '514E601E-FF49-4F93-A0D3-E4DFAD300EB3',
                    isShowTimeStamp: true,
                    plainMsg: {
                        id: 4251,
                        filelisttype: 1,
                        hostId: 'AVk_h4QBASY',
                        c: 'HyperText',
                        t: 'AVk_h4QBASY',
                        m: {
                            MIMETYPE: 'image/png',
                            si: 0,
                            meta: {
                                download: {
                                    fid: '6859kbl1n10f0321c1740001aa2b09',
                                    size: 2154600,
                                    sha256: '1f2854dbbff4004efc090e28572d177d3261bfd1ef8d62a9614670572fd46f6d',
                                    url: 'https://tech01-mapi.svc.matrx.tech/pfm/download/file?fid=6859kbl1n10f0321c1740001aa2b09&ts=1750667010817&id=uZl7pwwjoQyXb%5FGq&os=desktop',
                                    md5: '1f2854dbbff4004efc090e28572d177d3261bfd1ef8d62a9614670572fd46f6d'
                                },
                                filename: '60e53fe3c870e8c29f3f2e13720b9dd7.jpg',
                                hmacKey: '55b50541e88e65c7c5ecb67aae341b7cc3e99fa2822bdee6126bdc39d7db198c',
                                iKey: 'd11e91ac29dbcb90bb2bfac3d7294fd06b0a92863b454ab07d542ca38a6f59e8',
                                w: 1080,
                                h: 1621,
                                originSha256: '1f2854dbbff4004efc090e28572d177d3261bfd1ef8d62a9614670572fd46f6d',
                                progress: {
                                    enabled: true,
                                    loaded: 2154600,
                                    sendStatus: 'downloaded',
                                    total: 2154600,
                                    type: 'download',
                                    uploadOriginFilePath:
                                        'C:\\Users\\<USER>\\AppData\\Roaming\\Matrx-development\\UserData\\b29D1ofnD-w\\files\\514E601E-FF49-4F93-A0D3-E4DFAD300EB3\\6859kbl1n10f0321c1740001aa2b09\\60e53fe3c870e8c29f3f2e13720b9dd7.jpg'
                                }
                            },
                            nf: 0,
                            flags: 12,
                            lastSeq: 238,
                            ctime: 1750821017416,
                            stime: 1750821025531,
                            receipt: 0,
                            msgSeq: 239,
                            body: '[Image]',
                            uuid: '514E601E-FF49-4F93-A0D3-E4DFAD300EB3'
                        },
                        dialogId: 'b29D1ofnD-w',
                        peerId: 'AVk4Q4ELBF4',
                        expire: '1751821029629.0',
                        isMine: null,
                        messageType: 'group',
                        MIMETYPE: 'image/png',
                        ctime: 1750821017416,
                        stime: 1750821025531,
                        uuid: '514E601E-FF49-4F93-A0D3-E4DFAD300EB3',
                        body: '[Image]',
                        meta: {
                            download: {
                                fid: '6859kbl1n10f0321c1740001aa2b09',
                                size: 2154600,
                                sha256: '1f2854dbbff4004efc090e28572d177d3261bfd1ef8d62a9614670572fd46f6d',
                                url: 'https://tech01-mapi.svc.matrx.tech/pfm/download/file?fid=6859kbl1n10f0321c1740001aa2b09&ts=1750667010817&id=uZl7pwwjoQyXb%5FGq&os=desktop',
                                md5: '1f2854dbbff4004efc090e28572d177d3261bfd1ef8d62a9614670572fd46f6d'
                            },
                            filename: '60e53fe3c870e8c29f3f2e13720b9dd7.jpg',
                            hmacKey: '55b50541e88e65c7c5ecb67aae341b7cc3e99fa2822bdee6126bdc39d7db198c',
                            iKey: 'd11e91ac29dbcb90bb2bfac3d7294fd06b0a92863b454ab07d542ca38a6f59e8',
                            w: 1080,
                            h: 1621,
                            originSha256: '1f2854dbbff4004efc090e28572d177d3261bfd1ef8d62a9614670572fd46f6d',
                            progress: {enabled: true, loaded: 2154600, sendStatus: 'downloaded', total: 2154600, type: 'download'}
                        },
                        messageStatus: null,
                        filename: null,
                        isStar: null,
                        docId: null,
                        isNeedUpdateFTS: null,
                        isDeleted: null,
                        assertType: null,
                        finishTime: null,
                        assertPath:
                            'C:\\Users\\<USER>\\AppData\\Roaming\\Matrx-development\\UserData\\b29D1ofnD-w\\files\\514E601E-FF49-4F93-A0D3-E4DFAD300EB3\\6859kbl1n10f0321c1740001aa2b09\\60e53fe3c870e8c29f3f2e13720b9dd7.jpg',
                        fileFragmentMap: '',
                        content: {},
                        mcFrom: 'AE-971-6864514100#97170729544123486',
                        mcTo: 'AE-971-6864514100#8029711249307013100',
                        uuidRepliedRoot: null,
                        f: 'b29D1ofnD-w',
                        s: 'AVk4Q4ELBF4',
                        pinnedInfo: '',
                        unreadReceipt: '0',
                        receiptShow: 'show',
                        sessionHide: null,
                        extraData: {},
                        tenantId: '2961036487',
                        burned: null,
                        msgSeq: 239,
                        lastSeq: 238,
                        isShowTimeStamp: true,
                        isShowDate: true
                    },
                    stime: 1750821025531,
                    peerId: 'AVk4Q4ELBF4',
                    msgSeq: 239,
                    lastSeq: 238,
                    isShowDate: false,
                    unreadMessage: false,
                    isNotSupportForward: false
                },
                {
                    customData: {},
                    direct: 'LEFT',
                    token: 'AVk_h4QBASY',
                    chatComponentType: 'DOCUMENT',
                    timestamp: 1751617806490,
                    uuid: '16a197f0-58b1-11f0-9249-89dce7b6ea13',
                    isShowTimeStamp: true,
                    plainMsg: {
                        id: 6751,
                        filelisttype: 2,
                        hostId: 'AVk_h4QBASY',
                        c: 'HyperText',
                        t: 'b29D1ofnD-w',
                        m: {
                            MIMETYPE: 'x-filetransfer/octet-stream',
                            receipt: 255,
                            ctime: 1751617806490,
                            uuid: '16a197f0-58b1-11f0-9249-89dce7b6ea13',
                            nf: 0,
                            meta: {
                                isOrigin: 0,
                                iKey: 'dab3ceeeead9ac793191e907cd1826827d6a6800cf6304e6b91228d38ed4c059',
                                hmacKey: '4aca03a02d40d8cc0d9e6590b9682c51',
                                download: {fid: '6867a9tpsv910f9057da00013b169b', size: 147},
                                filename: 'mains (1).py',
                                originSha256: '4e2bf4d37fde68a444f8248bb4ca86d1a58ccbc0f64a2bbe62f5841ffd9ec8f3',
                                _: 'messageMediaPending',
                                fileType: 'x-filetransfer/octet-stream',
                                isReplaceIgnoreSymbol: false,
                                progress: {
                                    enabled: true,
                                    loaded: 147,
                                    sendStatus: 'uploaded',
                                    total: 147,
                                    type: 'upload',
                                    uploadOriginFilePath: 'C:\\Users\\<USER>\\Downloads\\mains (1).py'
                                }
                            },
                            flags: 22,
                            stime: 1751617806490,
                            time: 1751617807380
                        },
                        dialogId: 'b29D1ofnD-w',
                        peerId: 'AVk_h4QBASY',
                        expire: null,
                        isMine: '1',
                        messageType: 'group',
                        MIMETYPE: 'x-filetransfer/octet-stream',
                        ctime: 1751617806490,
                        stime: 1751617806490,
                        uuid: '16a197f0-58b1-11f0-9249-89dce7b6ea13',
                        body: null,
                        meta: {
                            isOrigin: 0,
                            iKey: 'dab3ceeeead9ac793191e907cd1826827d6a6800cf6304e6b91228d38ed4c059',
                            hmacKey: '4aca03a02d40d8cc0d9e6590b9682c51',
                            download: {fid: '6867a9tpsv910f9057da00013b169b', size: 147},
                            filename: 'mains (1).py',
                            originSha256: '4e2bf4d37fde68a444f8248bb4ca86d1a58ccbc0f64a2bbe62f5841ffd9ec8f3',
                            _: 'messageMediaPending',
                            fileType: 'x-filetransfer/octet-stream',
                            isReplaceIgnoreSymbol: false,
                            progress: {
                                enabled: true,
                                loaded: 147,
                                sendStatus: 'uploaded',
                                total: 147,
                                type: 'upload',
                                uploadOriginFilePath: 'C:\\Users\\<USER>\\Downloads\\mains (1).py'
                            }
                        },
                        messageStatus: 3,
                        filename: null,
                        isStar: null,
                        docId: null,
                        isNeedUpdateFTS: null,
                        isDeleted: null,
                        assertType: null,
                        finishTime: null,
                        assertPath: 'C:\\Users\\<USER>\\Downloads\\mains (1).py',
                        fileFragmentMap: '',
                        content: {},
                        mcFrom: 'AE-971-6864514100#97178718232969510',
                        mcTo: 'AE-971-6864514100#8029711249307013100',
                        uuidRepliedRoot: null,
                        f: 'AVk_h4QBASY',
                        s: 'AVk_h4QBASY',
                        pinnedInfo: '',
                        binaryPart: null,
                        unreadReceipt: '0',
                        receiptShow: 'show',
                        sessionHide: null,
                        extraData: {},
                        tenantId: null,
                        burned: null,
                        msgSeq: null,
                        lastSeq: null
                    },
                    stime: 1751617806490,
                    peerId: 'AVk_h4QBASY',
                    msgSeq: null,
                    lastSeq: null,
                    isShowDate: true,
                    unreadMessage: false,
                    isNotSupportForward: false
                },
                {
                    customData: {msg: 'dfasdfa'},
                    direct: 'LEFT',
                    token: 'AVk_h4QBASY',
                    chatComponentType: 'MESSAGE',
                    timestamp: 1751602995632,
                    uuid: '40D7BB06-2C99-4E70-A536-26302A7605F2',
                    isShowTimeStamp: true,
                    plainMsg: {
                        id: 6725,
                        filelisttype: 0,
                        hostId: 'AVk_h4QBASY',
                        c: 'HyperText',
                        t: 'DXouRf1EKRE',
                        m: {
                            MIMETYPE: 'text/plain',
                            si: 0,
                            meta: {},
                            nf: 0,
                            flags: 12,
                            lastSeq: 15,
                            ctime: 1751602995443,
                            stime: 1751602995632,
                            receipt: 0,
                            msgSeq: 16,
                            body: 'dfasdfa',
                            uuid: '40D7BB06-2C99-4E70-A536-26302A7605F2'
                        },
                        dialogId: 'DXouRf1EKRE',
                        peerId: 'AVk_h4QBASY',
                        expire: '1752602995613.0',
                        isMine: '1',
                        messageType: 'user',
                        MIMETYPE: 'text/plain',
                        ctime: 1751602995443,
                        stime: 1751602995632,
                        uuid: '40D7BB06-2C99-4E70-A536-26302A7605F2',
                        body: 'dfasdfa',
                        meta: {},
                        messageStatus: null,
                        filename: null,
                        isStar: null,
                        docId: null,
                        isNeedUpdateFTS: null,
                        isDeleted: null,
                        assertType: null,
                        finishTime: null,
                        assertPath: null,
                        fileFragmentMap: '',
                        content: {},
                        mcFrom: 'AE-971-6864514100#97178718232969510',
                        mcTo: 'AE-971-6864514100#971139547788486929',
                        uuidRepliedRoot: null,
                        f: 'AVk_h4QBASY',
                        s: null,
                        pinnedInfo: '',
                        binaryPart: null,
                        unreadReceipt: '0',
                        receiptShow: 'show',
                        sessionHide: null,
                        extraData: {},
                        tenantId: '2961036487',
                        burned: null,
                        msgSeq: 16,
                        lastSeq: 15,
                        isShowTimeStamp: true,
                        isShowDate: true
                    },
                    stime: 1751602995632,
                    peerId: 'AVk_h4QBASY',
                    msgSeq: 16,
                    lastSeq: 15,
                    isShowDate: false,
                    unreadMessage: false,
                    isNotSupportForward: false
                },
                {
                    customData: {msg: 'afd'},
                    direct: 'LEFT',
                    token: 'AVk_h4QBASY',
                    chatComponentType: 'MESSAGE',
                    timestamp: 1751605413306,
                    uuid: '3bdb5780-5894-11f0-b95b-45def0f980eb',
                    isShowTimeStamp: true,
                    plainMsg: {
                        id: 6732,
                        filelisttype: 0,
                        hostId: 'AVk_h4QBASY',
                        c: 'HyperText',
                        t: 'AVkHa4Q2doM',
                        m: {
                            ctime: 1751605413306,
                            uuid: '3bdb5780-5894-11f0-b95b-45def0f980eb',
                            MIMETYPE: 'text/plain',
                            body: 'afd',
                            nf: 0,
                            flags: 22,
                            stime: 1751605413306
                        },
                        dialogId: 'AVkHa4Q2doM',
                        peerId: 'AVk_h4QBASY',
                        expire: null,
                        isMine: '1',
                        messageType: 'user',
                        MIMETYPE: 'text/plain',
                        ctime: 1751605413306,
                        stime: 1751605413306,
                        uuid: '3bdb5780-5894-11f0-b95b-45def0f980eb',
                        body: 'afd',
                        meta: '',
                        messageStatus: 3,
                        filename: null,
                        isStar: null,
                        docId: null,
                        isNeedUpdateFTS: null,
                        isDeleted: null,
                        assertType: null,
                        finishTime: null,
                        assertPath: null,
                        fileFragmentMap: '',
                        content: {},
                        mcFrom: 'AE-971-6864514100#97178718232969510',
                        mcTo: 'AE-971-6864514100#97117025326233219',
                        uuidRepliedRoot: null,
                        f: 'AVk_h4QBASY',
                        s: null,
                        pinnedInfo: '',
                        binaryPart: null,
                        unreadReceipt: '2',
                        receiptShow: 'show',
                        sessionHide: null,
                        extraData: {},
                        tenantId: null,
                        burned: null,
                        msgSeq: 5,
                        lastSeq: 4,
                        isShowTimeStamp: true,
                        isShowDate: true
                    },
                    stime: 1751605413306,
                    peerId: 'AVk_h4QBASY',
                    msgSeq: 5,
                    lastSeq: 4,
                    isShowDate: false,
                    unreadMessage: false,
                    isNotSupportForward: false
                },
                {
                    customData: {
                        msg: 'curl "https://tech01-imsdk-gw.matrx.tech/sdk-msrs/rest/Msrs/getCombineMsgs?pattern=windows&clientver=1.23.0&rid=1751603046709&osver=10.0.19045&loc=en&pkg=unknow&randomnum=f22ed512-375d-4531-b612-7b71ca328b5b&clienttype=windows&deviceId=2d17c6f742a42734bbfbd8863572331be3ba44cf0bf1f394&enterpriseId=AE-971-6864514100&clientname=HUAWEI&clientnumber=LAPTOP-ER653LSA&ts=1751606656918" ^\n  -H "Accept: application/json, text/plain, */*" ^\n  -H "Accept-Language: zh-CN" ^\n  -H "Connection: keep-alive" ^\n  -H "Content-Type: application/json" ^\n  -H "Referer: http://localhost:8080/" ^\n  -H "Sec-Fetch-Dest: empty" ^\n  -H "Sec-Fetch-Mode: cors" ^\n  -H "Sec-Fetch-Site: cross-site" ^\n  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Matrx/1.23.0 Chrome/104.0.5112.124 Electron/20.3.8 Safari/537.36 matrx windows 1.23.0 matrxlan/en" ^\n  -H "access-token: 56c651ce6c5963b1054047ac10e98448b386a1cc4bb9019fc459b5737ba6ad5357c75059bfbd1c27" ^\n  -H "clienttype: windows" ^\n  -H "countrycode: 65" ^\n  -H "logid: W913ad3805dcf478b8b7c4eb4c62c0ad8" ^\n  -H "sec-ch-ua: ^\\^" Not A;Brand^\\^";v=^\\^"99^\\^", ^\\^"Chromium^\\^";v=^\\^"104^\\^"" ^\n  -H "sec-ch-ua-mobile: ?0" ^\n  -H "sec-ch-ua-platform: ^\\^"Windows^\\^"" ^\n  -H "token: 56c651ce6c5963b1054047ac10e98448b386a1cc4bb9019fc459b5737ba6ad5357c75059bfbd1c27" ^\n  --data-raw "^{^\\^"combineId^\\^":^\\^"D764D385-B9DE-436B-8813-F0BBC7DB0776^\\^",^\\^"pageNum^\\^":0,^\\^"pagSize^\\^":10,^\\^"eid^\\^":^\\^"AE-971-6864514100^\\^",^\\^"clientPub^\\^":^\\^"qlr9pGJvFOnvCAxyDdb/xlejJn8URJPsa0b5Ad4SDWw=^\\^"^}" ^\n  --compressed'
                    },
                    direct: 'LEFT',
                    token: 'AVk_h4QBASY',
                    chatComponentType: 'MESSAGE',
                    timestamp: 1751606685215,
                    uuid: '31f6f6e0-5897-11f0-be9d-dd05421be3d4',
                    isShowTimeStamp: true,
                    plainMsg: {
                        id: 6741,
                        filelisttype: 3,
                        hostId: 'AVk_h4QBASY',
                        c: 'HyperText',
                        t: 'DXouRf1EKRE',
                        m: {
                            ctime: 1751606685215,
                            uuid: '31f6f6e0-5897-11f0-be9d-dd05421be3d4',
                            MIMETYPE: 'text/plain',
                            body: 'curl "https://tech01-imsdk-gw.matrx.tech/sdk-msrs/rest/Msrs/getCombineMsgs?pattern=windows&clientver=1.23.0&rid=1751603046709&osver=10.0.19045&loc=en&pkg=unknow&randomnum=f22ed512-375d-4531-b612-7b71ca328b5b&clienttype=windows&deviceId=2d17c6f742a42734bbfbd8863572331be3ba44cf0bf1f394&enterpriseId=AE-971-6864514100&clientname=HUAWEI&clientnumber=LAPTOP-ER653LSA&ts=1751606656918" ^\n  -H "Accept: application/json, text/plain, */*" ^\n  -H "Accept-Language: zh-CN" ^\n  -H "Connection: keep-alive" ^\n  -H "Content-Type: application/json" ^\n  -H "Referer: http://localhost:8080/" ^\n  -H "Sec-Fetch-Dest: empty" ^\n  -H "Sec-Fetch-Mode: cors" ^\n  -H "Sec-Fetch-Site: cross-site" ^\n  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Matrx/1.23.0 Chrome/104.0.5112.124 Electron/20.3.8 Safari/537.36 matrx windows 1.23.0 matrxlan/en" ^\n  -H "access-token: 56c651ce6c5963b1054047ac10e98448b386a1cc4bb9019fc459b5737ba6ad5357c75059bfbd1c27" ^\n  -H "clienttype: windows" ^\n  -H "countrycode: 65" ^\n  -H "logid: W913ad3805dcf478b8b7c4eb4c62c0ad8" ^\n  -H "sec-ch-ua: ^\\^" Not A;Brand^\\^";v=^\\^"99^\\^", ^\\^"Chromium^\\^";v=^\\^"104^\\^"" ^\n  -H "sec-ch-ua-mobile: ?0" ^\n  -H "sec-ch-ua-platform: ^\\^"Windows^\\^"" ^\n  -H "token: 56c651ce6c5963b1054047ac10e98448b386a1cc4bb9019fc459b5737ba6ad5357c75059bfbd1c27" ^\n  --data-raw "^{^\\^"combineId^\\^":^\\^"D764D385-B9DE-436B-8813-F0BBC7DB0776^\\^",^\\^"pageNum^\\^":0,^\\^"pagSize^\\^":10,^\\^"eid^\\^":^\\^"AE-971-6864514100^\\^",^\\^"clientPub^\\^":^\\^"qlr9pGJvFOnvCAxyDdb/xlejJn8URJPsa0b5Ad4SDWw=^\\^"^}" ^\n  --compressed',
                            nf: 0,
                            flags: 22,
                            stime: 1751606685215
                        },
                        dialogId: 'DXouRf1EKRE',
                        peerId: 'AVk_h4QBASY',
                        expire: null,
                        isMine: '1',
                        messageType: 'user',
                        MIMETYPE: 'text/plain',
                        ctime: 1751606685215,
                        stime: 1751606685215,
                        uuid: '31f6f6e0-5897-11f0-be9d-dd05421be3d4',
                        body: 'curl "https://tech01-imsdk-gw.matrx.tech/sdk-msrs/rest/Msrs/getCombineMsgs?pattern=windows&clientver=1.23.0&rid=1751603046709&osver=10.0.19045&loc=en&pkg=unknow&randomnum=f22ed512-375d-4531-b612-7b71ca328b5b&clienttype=windows&deviceId=2d17c6f742a42734bbfbd8863572331be3ba44cf0bf1f394&enterpriseId=AE-971-6864514100&clientname=HUAWEI&clientnumber=LAPTOP-ER653LSA&ts=1751606656918" ^\n  -H "Accept: application/json, text/plain, */*" ^\n  -H "Accept-Language: zh-CN" ^\n  -H "Connection: keep-alive" ^\n  -H "Content-Type: application/json" ^\n  -H "Referer: http://localhost:8080/" ^\n  -H "Sec-Fetch-Dest: empty" ^\n  -H "Sec-Fetch-Mode: cors" ^\n  -H "Sec-Fetch-Site: cross-site" ^\n  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Matrx/1.23.0 Chrome/104.0.5112.124 Electron/20.3.8 Safari/537.36 matrx windows 1.23.0 matrxlan/en" ^\n  -H "access-token: 56c651ce6c5963b1054047ac10e98448b386a1cc4bb9019fc459b5737ba6ad5357c75059bfbd1c27" ^\n  -H "clienttype: windows" ^\n  -H "countrycode: 65" ^\n  -H "logid: W913ad3805dcf478b8b7c4eb4c62c0ad8" ^\n  -H "sec-ch-ua: ^\\^" Not A;Brand^\\^";v=^\\^"99^\\^", ^\\^"Chromium^\\^";v=^\\^"104^\\^"" ^\n  -H "sec-ch-ua-mobile: ?0" ^\n  -H "sec-ch-ua-platform: ^\\^"Windows^\\^"" ^\n  -H "token: 56c651ce6c5963b1054047ac10e98448b386a1cc4bb9019fc459b5737ba6ad5357c75059bfbd1c27" ^\n  --data-raw "^{^\\^"combineId^\\^":^\\^"D764D385-B9DE-436B-8813-F0BBC7DB0776^\\^",^\\^"pageNum^\\^":0,^\\^"pagSize^\\^":10,^\\^"eid^\\^":^\\^"AE-971-6864514100^\\^",^\\^"clientPub^\\^":^\\^"qlr9pGJvFOnvCAxyDdb/xlejJn8URJPsa0b5Ad4SDWw=^\\^"^}" ^\n  --compressed',
                        meta: '',
                        messageStatus: 3,
                        filename: null,
                        isStar: null,
                        docId: null,
                        isNeedUpdateFTS: null,
                        isDeleted: null,
                        assertType: null,
                        finishTime: null,
                        assertPath: null,
                        fileFragmentMap: '',
                        content: {},
                        mcFrom: 'AE-971-6864514100#97178718232969510',
                        mcTo: 'AE-971-6864514100#971139547788486929',
                        uuidRepliedRoot: null,
                        f: 'AVk_h4QBASY',
                        s: null,
                        pinnedInfo: '',
                        binaryPart: null,
                        unreadReceipt: '2',
                        receiptShow: 'show',
                        sessionHide: null,
                        extraData: {},
                        tenantId: null,
                        burned: null,
                        msgSeq: 23,
                        lastSeq: 22,
                        isShowTimeStamp: true,
                        isShowDate: true
                    },
                    stime: 1751606685215,
                    peerId: 'AVk_h4QBASY',
                    msgSeq: 23,
                    lastSeq: 22,
                    isShowDate: false,
                    unreadMessage: false,
                    isNotSupportForward: false
                }
            ]
        };
    },
    computed: {
        ...mapGetters('uiControl', ['chatContextMenuVisible'])
    },
    mounted() {
        this.queueManager = new QueueManager(1);

        document.body.addEventListener('click', this.closeRightPanel);
        Bus.$on('reply-right-option-visable', this.closeRightPanel);
        Bus.$on('SET_MESSAGE_TO_BE_FORWARD', this.gotMembersToBeForward);
    },
    beforeDestroy() {
        Bus.$off('SET_MESSAGE_TO_BE_FORWARD', this.gotMembersToBeForward);
    },
    methods: {
        ...mapActions('uiControl', ['setChatContextMenu']),
        ...mapActions('uiControl', ['openDialogCheckList']),
        scroll(e) {
            this.$emit('scrollHandle', e);
        },
        scrollHandle(dom, dis, direct) {
            this.$emit('scrollHandle', dom, dis, direct);
        },
        produceOptions() {
            let res = this.RIGHT_CLICK_OPTIONS();
            return res;
        },
        // 点击全局组件，关闭选项菜单
        closeRightPanel() {
            if (this.chatContextMenuVisible) {
                this.setChatContextMenu({visible: false});
            }
        },
        filterRightOptions(rightPanelOptions, info, source) {
            if (!Array.isArray(rightPanelOptions)) {
                console.error('rightPanelOptions is not an array');
                return [];
            }

            if (!info) {
                console.error('info is empty');
                return [];
            }

            if (this.isDebug) {
                rightPanelOptions.unshift({
                    label: 'Debug Copy',
                    type: 'debug',
                    handler: this.debugCopy
                });
            }

            // Only forward & copy
            if ((this.isSubscribe || dataUtil.getIsWhiteList(info.peerId)) && info.chatComponentType !== 'MEETINGCARD') {
                rightPanelOptions = rightPanelOptions.filter(({type} = {}) => ['forward', 'copy'].includes(type));
            }

            // Remove emoji
            if (source === 'TouchBar') {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'add_emoji');
            }

            // Remove delete
            if (info.peerId !== this.$store.state.userInfo.hid && info.chatComponentType !== 'MEETINGCARD') {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'delete');
            }

            // Remove retry
            if (!info.isFail) {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'retry');
            }

            // Remove select & forward & reply
            if (info.isFail || info.isSending) {
                rightPanelOptions = rightPanelOptions.filter(item => !['select', 'forward', 'reply'].includes(item.type));
            }

            // Remove join_meeting
            if (this.meetingStatus === 'pending') {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'join_meeting');
            }

            // Remove pin / unpin
            if (this.isGroup) {
                const isUnpin = info?.plainMsg?.pinnedInfo?.operation || info?.plainMsg?.m?.meta?.pinnedInfo?.pfrom;
                rightPanelOptions = rightPanelOptions.filter(item => (isUnpin ? item.type !== 'pin' : item.type !== 'unpin'));
            } else {
                rightPanelOptions = rightPanelOptions.filter(item => !['pin', 'unpin'].includes(item.type));
            }

            if (info.plainMsg?.MIMETYPE === 'poi/card') {
                // 如果是地图卡片 Copy，Forward，Select 不显示
                rightPanelOptions = rightPanelOptions.filter(
                    item => !(item.type === 'copy' || item.type === 'select' || item.type === 'forward')
                );
                const item = {
                    label: this.$t('map.open_new_tab'),
                    type: 'open_win',
                    handler: this.openPoiWin
                };
                const index = rightPanelOptions.findIndex(ele => ele.type === 'driver');

                if (index !== -1) {
                    rightPanelOptions.splice(index + 1, 0, item);
                }
            }

            // Remove edit
            if (info.chatComponentType === 'MESSAGE' || info.chatComponentType === 'MESSAGELONG') {
                // Filter editing function
                // Messages sent by oneself, and within 15 minutes.
                const canEdit =
                    info.peerId === this.$store.state.userInfo.hid &&
                    this.editMessageState.uuid !== info.uuid &&
                    (getTimestamp() - info.stime < 1000 * 60 * 15 || info.isFail);
                if (!canEdit) {
                    rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'edit');
                }
            }

            // Remove translate / hide_translate
            if (info.chatComponentType === 'MESSAGE' || info.chatComponentType === 'MESSAGELONG' || info.chatComponentType === 'RICHTEXT') {
                // Filter translate / hide translate option
                const canTranslate = this.checkCanTranslate(info.plainMsg);
                if (this.isUseMessageTranslation && canTranslate) {
                    if (info?.plainMsg?.extraData?.translate) {
                        rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'translate');
                    } else {
                        rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'hide_translate');
                    }
                } else {
                    rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'hide_translate' && item.type !== 'translate');
                }
            }

            // Remove comment & copy
            if (info.chatComponentType === 'Announcement' && info?.plainMsg?.m?.meta?.announcementInfo?.delete) {
                rightPanelOptions = rightPanelOptions.filter(item => !['comment', 'copy'].includes(item.type));
            }

            // Remove comment
            if (info.chatComponentType === 'MESSAGE' && info?.plainMsg?.m?.meta?.announcementInfo?.delete) {
                rightPanelOptions = rightPanelOptions.filter(item => !['comment'].includes(item.type));
            }

            // Filter driver
            rightPanelOptions = rightPanelOptions.reduce((result, item, index, arr) => {
                if (item.type === 'driver') {
                    const preElement = result[result.length - 1];
                    const isNotLastElement = !(index === arr.length - 1 && item.type === 'driver');
                    if (result.length > 0 && preElement.type !== 'driver' && isNotLastElement) {
                        result.push(item);
                    }
                } else {
                    result.push(item);
                }
                return result;
            }, []);
            console.log('rightPanelOptions', rightPanelOptions);
            return rightPanelOptions.filter(
                item => item.type === 'forward' || item.type === 'copy' || item.type === 'save' || item.type === 'open_file_location'
            );
        },
        // 转发
        async gotMembersToBeForward(members) {
            try {
                console.time('getMembersToBeForward--');
                F.checkCurrentSpaceExpire();

                let viewChatCheckedList = this.viewChatCheckedList;
                const isMultipleSelected = this.isMultipleSelected;
                const currentChatInfo = this.currentChatInfo;
                if (!isMultipleSelected) {
                    // 是否为多选状态
                    viewChatCheckedList = [currentChatInfo];
                }
                // 限制发送消息的顺序
                // 逐条转发消息管理
                // const queueManager = new QueueManager(1);
                // const queueManager = this.queueManager;

                for (const member of members) {
                    const memberId = member.id;
                    for (const item of viewChatCheckedList) {
                        // const handler = F.forwords(item.plainMsg.uuid, item.isExistGroupPinList, item.plainMsg);
                        // ipcRenderer.send('COMBINE_EVENT_FORWAED', {
                        //     uuid: item.plainMsg.uuid,
                        //     isExistGroupPinList: item.isExistGroupPinList,
                        //     plainMsg: item.plainMsg
                        // });
                        ipcRenderer.send(
                            'SEND-CURRENT-WIN-MSG',
                            {
                                type: 'COMBINE_EVENT_FORWAED',
                                data: {
                                    memberId,
                                    uuid: item.plainMsg.uuid,
                                    isExistGroupPinList: item.isExistGroupPinList,
                                    plainMsg: item.plainMsg
                                }
                            },
                            ''
                        );
                        // queueManager
                        //     .push({
                        //         uuid: memberId, // 自动根据根据 memberId 分组
                        //         fetch: handler.bind(this, [memberId])
                        //     })
                        //     .then(() => {})
                        //     .catch(error => {
                        //         console.error('[error]: ', 'error :>> ', error);
                        //     });
                    }
                }
                // 关闭group pin 面板
                // this.closeGroupPannel();
                this.$message({
                    message: this.$t('right_content_panel.forward_message_success'),
                    type: 'success'
                });
                console.timeEnd('getMembersToBeForward--');
                if (members.length === 1) {
                    let dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
                    const st = setTimeout(() => {
                        this.$store.dispatch('uiControl/switchOne', {hid: members[0].id, spaceId: defaultSpaceId()});
                        window.clearTimeout(st);
                        requestAnimationFrame(() => {
                            if (dom) {
                                dom.scrollTop = 0;
                            }
                            dom = null;
                        });
                    }, 500);
                }
            } catch (error) {
                if (error && error.msg) {
                    this.$message({
                        message: error.msg,
                        type: 'error'
                    });
                }
                console.error('[error]: ', 'gotMembersToBeForward', error);
            } finally {
                this.$store.commit('uiControl/setMultipleSelected', false);
                // this.openedMembersToBeForward = false;
                this.$nextTick(() => {
                    if (!this.candown) {
                        requestAnimationFrame(() => {
                            let dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
                            dom && (dom.scrollTop = 0);
                            dom = null;
                        });
                    }
                    this.$refs?.vuetobeadded?.resetData();
                    this.$refs?.vuetobeadded?.resetSearchData(true);
                });
            }
        },
        // 点击右键，打开选项
        async openRightPanel($event, info, source) {
            console.log('openRightOption: ', info, source);
            if (F.isLocationShare(info.plainMsg)) {
                // 如果是共享实时位置 不显示
                return;
            }

            if ($event.target.classList.contains('message-bar-content')) {
                return;
            }
            // 停止播放
            Bus.$emit('voice-play', '');
            this.rightPanelOptions = [];
            this.currentChatInfo = info;
            // 定位选项box到鼠标右键处
            // 280 - 左侧sessionlist定宽
            // 61 - 顶部定高
            // 10 - 增加偏移20 避免激活时误操作

            const sessionWidth = appdataStorage.getItem('currentSessionWidth') || 296;
            const leftMenuWidth = 64;

            let maxLeft = window.innerWidth - 170 - 64 + 20;
            this.tranLeft = $event.pageX + 10 > maxLeft ? maxLeft - 120 : $event.pageX + 10;
            if (this.$i18n.locale === 'ar') this.tranLeft = $event.pageX + 10 > maxLeft ? maxLeft - 120 + 150 : $event.pageX + 150 + 10;
            this.tranTop = $event.pageY - 91 + 10;
            this.tranRight = 'auto';

            if (source === 'TouchBar') {
                try {
                    const touchBarEle = $event.target.closest('.touch-bar');
                    const {right, top, left} = touchBarEle.getBoundingClientRect();

                    this.tranTop = top - 40;

                    if (this.$i18n.locale === 'ar') {
                        if (left < 200) {
                            this.tranLeft = right + 2;
                            this.tranRight = 'auto';
                        } else {
                            this.tranRight = window.innerWidth - left - sessionWidth - leftMenuWidth + 2;
                            this.tranLeft = 'auto';
                        }
                    } else {
                        if (right + 200 > window.innerWidth) {
                            this.tranLeft = 'auto';
                            this.tranRight = window.innerWidth - left + 2;
                        } else {
                            this.tranLeft = right + 2 - sessionWidth - leftMenuWidth;
                            this.tranRight = 'auto';
                        }
                    }
                } catch (error) {
                    console.log(`🚀 ~ openRightPanel ~ error:`, error);
                }
            }

            const wrapper = document.getElementsByClassName('message-box')[0];
            const wrapperHeight = parseInt(getComputedStyle(wrapper).height);

            // 垂直偏移 + 弹出层高度 > 总容器高度 - 输入框高度
            // 上移弹出层
            // 设置右键菜单列表项
            const progress =
                this.$store.state.fileProcessCollection.fileList[info?.plainMsg?.dialogId]?.[info?.plainMsg?.m?.uuid]?.progress ||
                F.paths(info, 'plainMsg', 'm', 'meta', 'progress');

            let sendStatus = progress?.sendStatus;
            let type = F.paths(info, 'plainMsg', 'm', 'meta', 'type');
            let status = sendStatus || 'default';
            let allRightOptions = [];
            let announcementId = '';
            if (info.chatComponentType === 'MESSAGE')
                announcementId = F.paths(info, 'plainMsg', 'm', 'meta', 'announcementInfo', 'announcementId');

            if (info.isExistReplyList || info.isExistGroupPinList) {
                // 回复列表
                allRightOptions = this.produceReplyListRightOptions(info);
                // 回复列表数据来之服务器，文件状态每次都为空
                if (info?.plainMsg?.MIMETYPE == 'x-filetransfer/octet-stream') {
                    let isFileExist = await this.checkFileisExist(info);
                    if (isFileExist) {
                        status = 'downloaded';
                    }
                }
            } else {
                allRightOptions = this.produceOptions(info);
            }
            // 文件上传 消息部分失败，需要修改右边菜单
            if (info.chatComponentType == 'DOCUMENT' && status == 'uploaded' && info.isFail) {
                status = 'uploadPause';
            }

            let options = status && F.paths(allRightOptions, announcementId ? 'AnnouncementCommit' : info.chatComponentType, status);

            if (
                info.chatComponentType === 'PICTURE' &&
                status === 'default' &&
                F.paths(info, 'plainMsg', 'm', 'meta', 'progress', 'type') === 'download'
            ) {
                // 图片default状态，区分上传还是下载，下载得话没有 cancel 菜单项
                options = options.filter(item => item.type !== 'cancel');
            }
            // 跨行菜单仅留复制
            options = F.multiLineMenus(options, info);

            if (options) {
                let temp = options.slice();

                if (type === 'meeting-assistant-msg-del') {
                    // 只保留删除选项
                    temp.shift();
                    temp.shift();
                }

                this.rightPanelOptions = this.filterRightOptions(temp, info, source);
            } else if (info.chatComponentType === 'UNKNOW') {
                this.rightPanelOptions = this.filterRightOptions(allRightOptions[info.chatComponentType], info, source);
            } else {
                let temp = allRightOptions['DEFAULT'].slice();
                this.rightPanelOptions = this.filterRightOptions(temp, info, source);
            }
            if (this.rightPanelOptions && this.rightPanelOptions <= 0) {
                return;
            }

            this.$nextTick(() => {
                const rightOption = this.$refs.rightClickOption;
                const optionHeight = parseInt(getComputedStyle(rightOption.$el).height);
                // console.log('rightOption', rightOption);
                // this.$i18n.locale === 'ar'
                if (this.tranTop + optionHeight > wrapperHeight - 150) {
                    this.tranTop = this.tranTop - optionHeight - 20 + 60;
                }
            });
            // 最后显示选择框，避免瞬间移动

            if (info.uuid === this.currentChatInfo?.uuid) {
                this.setChatContextMenu({visible: true, uuid: info.uuid, eventTrigger: source});
            } else {
                console.error('uuid change for openRightPanel');
            }
        },
        chatTypeComputed(chatItem, isSubscribe) {
            let componentName = CHAT_TYPE[chatItem.chatComponentType];
            return {
                is: componentName,
                isSubscribe,
                chatInfo: chatItem
            };
        },
        // Save as...保存图片 保存文件
        async savePictureMessage() {
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                let filePath = this.currentChatInfo.plainMsg.assertPath;
                if (!filePath && this.$store.state.uiControl.replyListPannelVisible) {
                    filePath = await this.checkFileisExist(this.currentChatInfo);
                }
                if (!existFile(filePath)) {
                    this.$message(this.$t('errorCode.error_save_as'));

                    this.currentChatInfo.plainMsg.m.meta.progress.type = 'download';

                    this.currentChatInfo.plainMsg.m.meta.progress.sendStatus = '';
                    this.currentChatInfo.plainMsg.m.meta.progress.loaded = 0;

                    this.$store.commit('fileProcessCollection/updateFileProcess', {
                        dialogId: this.currentChatInfo.plainMsg.dialogId,
                        messageId: this.currentChatInfo.plainMsg.m.uuid,
                        progress: {
                            ...this.currentChatInfo.plainMsg.m.meta.progress
                        }
                    });

                    this.$store.dispatch('messageCollection/saveMessage', {message: this.currentChatInfo.plainMsg});

                    this.$store.commit('fileWorking/delFileWorkObj', this.currentChatInfo.plainMsg.m.uuid);

                    return;
                }

                filePath && ipcRenderer.send('SAVE-AS-PIC', filePath);
            });
        },
        // Save as...保存图片 保存文件
        async openFileLocationMessage() {
            try {
                let filePath = this.currentChatInfo.plainMsg.assertPath;
                if (!filePath && this.$store.state.uiControl.replyListPannelVisible) {
                    filePath = await this.checkFileisExist(this.currentChatInfo);
                }
                if (filePath && Tool.existFile(filePath)) {
                    ipcRenderer.send('OPEN-FILE-LOCATION', filePath);
                } else {
                    const plainMsg = this.currentChatInfo.plainMsg;

                    plainMsg.m.meta.progress.type = 'download';

                    this.$store.commit('fileCancelCollection/setFileCancelObj', {
                        uuid: plainMsg.m.uuid,
                        isCancel: false
                    });

                    plainMsg.m.meta.progress.sendStatus = '';
                    plainMsg.m.meta.progress.loaded = 0;

                    this.$store.commit('fileProcessCollection/updateFileProcess', {
                        dialogId: plainMsg.dialogId,
                        messageId: plainMsg.m.uuid,
                        progress: {
                            ...plainMsg.m.meta.progress
                        }
                    });

                    this.$store.dispatch('messageCollection/saveMessage', {message: plainMsg});

                    this.$store.commit('fileWorking/delFileWorkObj', plainMsg.m.uuid);
                }
            } catch (error) {
                console.error('OPEN-FILE-LOCATION error', error);
            }
        },
        // 转发文件消息
        forwardDocumentMessage() {
            this.$nextTick(() => {
                this.$refs.vuetobeadded?.resetData();
                this.$refs?.vuetobeadded?.resetSearchData(true);
            });
            this.typeNameMembersToBeForward = 'forwardSelect';
            // this.openedMembersToBeForward = true;
            this.openDialogCheckList('forwardMessage');
        },
        // 转发文本消息
        forwardTextMessage(info) {
            this.$nextTick(() => {
                if (info) {
                    this.currentChatInfo = info;
                }
                this.$refs?.vuetobeadded?.resetData();
                this.$refs?.vuetobeadded?.resetSearchData(true);
            });
            this.typeNameMembersToBeForward = 'forwardSelect';
            // this.openedMembersToBeForward = true;
            this.openDialogCheckList('forwardMessage');
        },
        async copyFile() {
            let filePath = this.currentChatInfo.plainMsg.assertPath;
            if (filePath) {
                await setClipboard('img', filePath);
                this.$message.success(this.$t('right_content_panel.copySuccess'));
            }
        },
        copyMessageHandler(info) {
            console.log('copyMessageHandler', info);
            if (window.getSelection().toString()) {
                document.execCommand('copy');
                window.getSelection().empty();
            } else {
                let data;
                const restrictedType = F.paths(info, 'plainMsg', 'm', 'meta', 'restrictedType');
                if (info.chatComponentType === 'RESTRICTED' && restrictedType && msgRestrictedTypeInfo[restrictedType]) {
                    const str = msgRestrictedTypeInfo[restrictedType];
                    data = str ? `[${str}]` : data;
                } else if (info.chatComponentType === 'RICHTEXT' || info.chatComponentType === 'Announcement') {
                    data = parseMarkdownToPlainText(info.plainMsg.body);
                } else {
                    data = info.plainMsg.body;
                }

                /* write to the clipboard now */
                navigator.clipboard.writeText(data);
            }
            this.$message.success(this.$t('right_content_panel.copySuccess'));
        }
    }
};
</script>

<style lang="scss" scoped>
.view-scroll-wrap {
    width: 100%;
    flex: 1;
    overflow: hidden;
    background: var(--grey-1);
}
.chatscroll-container {
    position: relative;
    height: 100%;
    width: calc(100% - 6px);
    padding-bottom: 30px;
    box-sizing: border-box;
    background: var(--grey-1);
}

.message-loading {
    width: 100%;
    height: 20px;
    margin-left: 6px;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}
.el-popper.popover-screen-capture {
    border-radius: 4px;
    background-color: var(--grey-9);
    padding: 7px 8px;
    font-family: var(--font-family-normal);
    font-size: 12px;
    line-height: 14px;
    color: var(--grey-4);
    margin-bottom: 0px;
    margin-top: 8px;
    box-shadow: var(--box-shadow-select-dropdown);
    border: none;
    &.down-icon {
        padding: 2px 0;
        background-color: var(--grey-0);
        .form-wrap {
            padding: 9px 16px;
            &:hover {
                background-color: var(--grey-1);
            }
            &.is-checked {
                // background-color: var(--grey-2);
            }
        }
        .el-checkbox__label {
            color: var(--grey-10);
            font-size: 14px;
            line-height: 18px;
            padding-left: 12px;
        }
    }
    .el-popover__title {
        margin-bottom: 0px;
        font-family: var(--font-family-normal);
        font-size: 12px;
        line-height: 14px;
        color: var(--grey-0);
    }
}

.download-file-dialog {
    width: 456px;
    border-radius: 10px;
    .el-dialog__title {
        font-family: var(--font-family-normal);
        font-size: 16px;
        font-weight: 600;
        line-height: 1.25;
        text-align: left;
        color: var(--grey-10);
    }
    .el-button {
        border-radius: 4px;
        background-color: var(--grey-2);
        font-family: var(--font-family-normal);
        height: 32px;
        font-size: 14px;
        text-align: center;
        color: var(--grey-10);
        line-height: 6px;
        &:hover {
            background: var(--grey-3);
            border: 1px solid var(--grey-3);
        }
    }
    .el-button--primary {
        background-color: var(--blue-4);
        color: var(--grey-0);
        &:hover {
            background: var(--blue-5);
            border: 1px solid var(--blue-5);
        }
    }
    .el-dialog__body {
        padding: 16px 20px;
        font-size: 14px;
        line-height: 1.29;
        color: var(--grey-10);
    }
    .el-dialog__header {
        border-bottom: 1px solid var(--grey-1);
    }

    .content {
        .icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--orange-4);
            color: var(--grey-0);
            font-size: var(--font-size-10);
            text-align: center;
        }
        .title {
            font-family: var(--font-family-normal);
            font-size: 14px;
            line-height: 1.29;
            text-align: left;
            color: var(--grey-10);
            word-break: keep-all;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        .disk-tips {
            color: var(--red-4);
            margin-top: 10px;
        }
        .location {
            display: flex;
            flex-direction: row;
            margin-top: 6px;
            position: relative;

            .download-path {
                width: 316px;
                height: 32px;
                line-height: 32px;
                padding: 0px 7px;
                border-radius: 4px;
                border: solid 1px var(--grey-2);
                background-color: var(--grey-1);

                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .download-file-btn {
                border-radius: 4px;
                background-color: var(--grey-2);
                vertical-align: middle;
                text-align: center;
                padding: 8px 28px;
                cursor: pointer;
                margin-left: 4px;
                &:hover {
                    background: var(--grey-3);
                }
            }
        }
        .tip {
            display: flex;
            margin-top: 16px;
            margin-left: 4px;
        }
        .perference-tip {
            font-family: var(--font-family-normal);
            font-size: 12px;
            font-weight: 500;
            line-height: 1.17;
            text-align: left;
            color: var(--orange-4);
            margin-left: 4px;
        }
    }
}
.group-pin-top-ten-dialog {
    border-radius: 10px;
    max-width: 456px;
    .el-dialog__title {
        font-family: var(--font-family-normal);
        font-size: 14px;
        font-weight: 500;
        line-height: 1.29;
        color: #141414;
    }
    .el-dialog__body {
        font-family: var(--font-family-normal);
        background-color: var(--grey-1);
        font-size: 14px;
        line-height: 1.29;
        color: var(--grey-8);
        padding: 16px 24px;
    }
    .el-dialog__footer {
        background-color: var(--grey-1);
        border-radius: 0px 0px 10px 10px;
    }
    .el-button {
        padding: 8px 20px;
        font-size: 14px;
        border-radius: 4px;
        background-color: var(--blue-4);
        color: #fff;
        width: 88px;
    }
    .body {
        word-break: break-word;
        font-size: 13.6px;
    }
}
.group-unpin-dialog {
    border-radius: 10px;
    max-width: 456px;
    .el-dialog__title {
        font-family: var(--font-family-normal);
        font-size: 14px;
        font-weight: 500;
        line-height: 1.29;
        color: var(--grey-10);
    }
    .el-dialog__body {
        font-family: var(--font-family-normal);
        background-color: var(--grey-1);
        font-size: 14px;
        line-height: 1.29;
        color: var(--grey-8);
        padding: 16px 24px;
    }
    .el-dialog__footer {
        background-color: var(--grey-1);
        border-radius: 0px 0px 10px 10px;
    }
    .el-button {
        padding: 8px 20px;
        font-size: 14px;
        border-radius: 4px;
        background-color: var(--blue-4);
        color: #fff;
        min-width: 88px;
        outline: none;
    }
    .cancelBtn {
        background-color: #d9d9d9;
        color: var(--blue-4);
        border: none;
    }
    .body {
        word-break: break-word;
    }
}

.view-scroll-wrap.isMultipleSelected {
    .e2e_wrap {
        pointer-events: none;
        user-select: none;
        margin: 0px 100px;
    }
}
.popover-contact {
    padding: 0;
    border: 0px solid transparent;
    background-color: transparent;
    box-shadow: none;
}
.chats_-tooltip {
    margin-top: 8px !important;
    padding: 7px 8px !important;
}
</style>
<style lang="scss" scoped>
.suggestion-backtop {
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 100%;
}
.select-footer-bar {
    padding: 0 24px;
    border-top: 1px solid var(--grey-2);
    box-sizing: border-box;
    height: 74px;
    background-color: var(--grey-0);
    .flex-around-center {
        flex-grow: 1;
    }
    .select-share-item {
        text-align: center;
    }
    .select-item-icon {
        width: 32px;
        height: 32px;
        padding: 8px;
        box-sizing: border-box;
        border-radius: 10px;
        background: var(--grey-1);
        margin: 0 auto 4px;
        :deep(.svg-icon) {
            color: #434343;
        }
    }
}
.menu {
    position: relative;
}

.chatmessage-container {
    // padding-left: 24px;
    // padding-right: 24px;
    // box-sizing: border-box;
}
.dialog-content {
    position: relative;
    height: 65vh;
    width: 100%;
    .chatscroll-container {
        position: relative;
        height: 100%;
        width: calc(100% - 6px);
        padding-top: 35px;
        padding-bottom: 30px;
        box-sizing: border-box;
        background: var(--grey-1);
    }
    .message_cover {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 1);
    }
    .above_box {
        position: absolute;
        bottom: 117px;
        right: 15px;
        width: 99%;
    }
}
.svgicon {
    font-size: var(--font-size-46);
    color: var(--yellow-6);
}
.dialog-container {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    background-color: var(--grey-0);
    flex: 1;
    overflow: hidden;
    border-right: 2px solid var(--grey-0);
}
.not_readlistinfo {
    margin: 0 auto;
    display: flex;
    width: 90%;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .listinfo {
        margin: 0 10px;
        width: 45%;
        text-align: center;
        box-sizing: border-box;
        .scroll-content-wrap {
            margin: 0 auto;
            height: 600px;
            overflow-x: hidden;
            overflow-y: scroll;
        }
        .list-wrap-item {
            padding-bottom: 30px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            .nickname {
                display: inline-block;
                padding: 0 3rem 0 1rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
.message-hidden-input {
    position: absolute;
    top: 0;
    left: -9999999999px;
    padding: 0;
    margin: 0;
    width: 0;
    height: 0;
}
.message-box {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    &.show {
        transform: translate(0px, 1000px);
    }
    &.showed {
        transition: all 0.1s linear;
        transform: translate(0px, 0px);
    }
    .view-scroll-wrap {
        width: 100%;
        flex: 1;
        overflow: hidden;
        background: var(--grey-1);
    }
    .message-list {
        width: 100%;
        height: 647px;
        overflow-y: auto;
        background: var(--grey-0);

        .chat-item-box {
            display: flex;
            justify-content: flex-start;
            align-items: flex-end;
            padding: 0px 20px;
            margin-bottom: 3px;

            .chat-portrait {
                width: 30px;
                height: 30px;
                padding: 0px 6px;
                margin: 0px;
            }
            .chat-item {
                position: relative;
                max-width: 420px;
                min-width: 10px;
                min-height: 10px;
                border-radius: 6px;
                background-color: var(--grey-1);
                padding: 8px 15px;
                &_nickname {
                    font-size: var(--font-size-12);
                    color: var(--blue-4);
                }
                &:after {
                    content: '';
                    position: absolute;
                    bottom: 0px;
                    right: 0px;
                    left: -8px;
                    width: 10px;
                    height: 10px;
                    border: 10px solid transparent;
                    border-bottom: 10px solid var(--grey-1);
                }
            }
        }
        .chat-item-box--self {
            justify-content: flex-end;
            align-items: flex-end;
            .chat-item {
                background: var(--sky-blue-2);
                &:after {
                    content: '';
                    position: absolute;
                    bottom: 0px;
                    right: -8px;
                    left: unset;
                    width: 10px;
                    height: 10px;
                    border: 10px solid transparent;
                    border-bottom: 10px solid var(--sky-blue-2);
                }
            }
        }
        .split-message {
            margin-bottom: 10px;
        }
    }
    .message-sendbox-container {
        position: relative;
        width: 100%;
    }
    .message-sendboxwrap {
        box-sizing: border-box;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px 24px 20px 24px;
        // overflow: hidden;
        // 添加了左侧边栏，需在原来的基础上再减64px
        height: 170px;
        border-top: 1px solid var(--grey-2);
        background: var(--grey-1);
        z-index: 3;
        &.message-box-remove {
            display: flex;
            flex-direction: row;
            padding: 0 25px;
            justify-content: flex-start;
            align-items: center;
            .person-removed {
                color: var(--grey-5);
                font-size: var(--font-size-14);
            }
        }
        .message-sendbox {
            width: calc(100% - 56px);
            padding-right: 5px;
            height: 100%;
            font-size: var(--font-size-15);
            color: var(--grey-10);
            outline: none;
            overflow: auto;
            word-break: normal;
            &::placeholder {
                color: var(--grey-4);
            }
        }
        .message-sendbox-icon {
            position: absolute;
            bottom: 24px;
            right: 24px;
            width: 40px;
            height: 40px;
            background-color: #ccc;
            box-sizing: border-box;
            padding: 8px 7px 8px 9px;
            border-radius: 50%;
            .svg-icon {
                color: var(--grey-0);
            }
            &.active {
                background-color: var(--blue-4);
            }
        }
        .action-icon-group {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 22px;
            .chat_icon {
                width: 24px;
                height: 24px;
                margin-right: 21px;
                cursor: pointer;
                user-select: none;
                outline: none;
                background-repeat: no-repeat;
                background-position: center;
                border-radius: 4px;
                &:last-child {
                    margin-right: 0;
                }
                &.screen_capture_wrap {
                    width: 30px;
                    display: flex;
                    align-items: center;
                    flex-flow: row nowrap;
                    justify-content: space-between;
                    .screen-capture {
                        width: 24px;
                        height: 24px;
                        // background: url('../assets/chat/msgInput/screen_capture.svg') no-repeat;
                        outline: none;
                        border-radius: 4px;
                        &:hover,
                        &.active {
                            background-color: var(--black-05);
                        }
                    }
                    .down-icon {
                        width: 12px;
                        height: 24px;
                        // background: url('../assets/chat/msgInput/ic_back_hover.svg') no-repeat;
                        background-position: center;
                        border-radius: 4px;
                        outline: none;
                        margin-left: -2px;
                        &:hover,
                        &.active {
                            background-color: var(--black-05);
                        }
                    }
                }
            }
            .emoji_icon {
                // background-image: url('../assets/chat/msgInput/emoji_icon.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .namecard_icon {
                // background-image: url('../assets/chat/msgInput/namecard_icon.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .mention_icon {
                // background-image: url('../assets/chat/msgInput/mention_icon.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .fileIcon_icon {
                // background-image: url('../assets/chat/msgInput/ic_message_file.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .imageIcon_icon {
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .upimfile {
                font-size: 0;
                .icon {
                    width: 18px;
                    height: 18px;
                    cursor: pointer;
                }
            }
        }
    }

    .blocked-message-sendboxwrap {
        display: flex;
        align-items: center;
        justify-content: center;
        & .unblock-span {
            color: var(--blue-4);
            cursor: pointer;
        }
    }
    .message-sendboxwrap--faile {
        position: absolute;
        bottom: 0px;
        left: 0px;
        z-index: 4;
        height: 73px;
        opacity: 0;
    }
}
.inputStyle {
    position: absolute;
    left: 164px;
    width: 120px;
    display: none;
}
[dir='rtl'] .download-file-dialog .content .location .download-file-btn {
    padding: 8px 0;
    flex: 1;
}
</style>
