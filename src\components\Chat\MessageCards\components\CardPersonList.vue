<template>
  <div>

  </div>
</template>

<script>
  export default {
    name: 'CardPersonList',
    data () {
      return {
        personListData: {
          "tag": "person_list",
          "lines": 1, // 最大显示行数，默认关闭不限制最大显示行数。
          "show_name": true, // 是否展示人员对应的用户名。
          "show_avatar": true, // 是否展示人员对应的头像。
          "size": "large", // 人员头像的尺寸。
          "persons": [
            // 人员列表。人员的 ID 支持 open_id , user_id, union_id
            {
              "id": "ou_0fdb0e7663af7128e7d9f8adeb2abcef"
            },
            {
              "id": "ou_0fdb0e7663af7128e7d9f8adeb2abcef"
            }
          ],
          "icon": {
            // 前缀图标
            "img_key": "img_v2_38811724" // 图片的 key。仅在 tag 为 custom_icon 时生效。
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>