import store from '@/store';
import {copyInvitation, transformCstCycleParams} from '@/utils/meeting';
import {fireMessageSelect} from '@/Firebase';
import {checkConfIdSdk} from '@/utils/sdk/meetingUtils';
import {cloneDeep} from 'lodash';
import {joinMeetingEnum} from '@/utils/report/reportUtil';
import MeetingService from '@/utils/meeting/meetingService.js';
import {mapGetters} from 'vuex';
import {ipcRenderer} from 'electron';

function filterOptions(options) {
    const isSaveMultiMedia = store.getters['spaceLimit/isSaveMultiMedia'];
    const isRemoteChatDownloadPath = store.state.spaceLimit.isRemoteChatDownloadPath;

    // const serverConfigInfo = store.state?.config?.serverConfigInfo || {};
    const picShare = isSaveMultiMedia;
    const fileShare = isSaveMultiMedia;
    // 过滤掉 图片的save
    if (picShare === false) {
        Object.keys(options.PICTURE).forEach(key => {
            if (Array.isArray(options.PICTURE[key])) {
                options.PICTURE[key] = options.PICTURE[key].filter(item => !(item.type === 'save' || item.type === 'open_file_location'));
            }
        });
    }
    // 过滤掉 文件的save
    if (fileShare === false) {
        Object.keys(options.DOCUMENT).forEach(key => {
            if (Array.isArray(options.DOCUMENT[key])) {
                options.DOCUMENT[key] = options.DOCUMENT[key].filter(item => !(item.type === 'save' || item.type === 'open_file_location'));
            }
        });
    }

    if (isRemoteChatDownloadPath) {
        Object.keys(options.DOCUMENT).forEach(key => {
            if (Array.isArray(options.DOCUMENT[key])) {
                options.DOCUMENT[key] = options.DOCUMENT[key].filter(item => !(item.type === 'save'));
            }
        });
    }
    return options;
}

export default {
    data() {
        return {};
    },
    computed: {
        ...mapGetters('spaceLimit', ['isAudioForward', 'isSaveMultiMedia'])
    },
    methods: {
        REPLY_RIGHT_CLICK_OPTIONS() {
            const options = {
                UNKNOW: [
                    // {
                    //   label: this.$t("right_content_panel.right_click_panel_reply"),
                    //   type: 'reply',
                    //   handler: this.replyHandler
                    // }
                ],

                DEFAULT: [
                    {
                        label: this.$t('right_content_panel.right_click_panel_copy'),
                        type: 'copy',
                        handler: this.copyMessageHandler
                    }
                ],
                CALLRECORD: {
                    default: []
                },
                VCARD: {
                    default: []
                },
                VOICE: {
                    default: []
                },

                MEETINGINVITE: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMeetingInviteMessageHandler
                        }
                    ]
                },
                DOCUMENT: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_download'),
                            type: 'download',
                            handler: this.$refs[this.currentChatInfo.uuid]?.documentOperationHandle
                        }
                    ],
                    downloaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_open_file_location'),
                            type: 'open_file_location',
                            handler: this.openFileLocationMessage
                        }
                    ],
                    uploaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_open_file_location'),
                            type: 'open_file_location',
                            handler: this.openFileLocationMessage
                        }
                    ]
                },
                PICTURE: {
                    download: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        }
                    ],
                    uploaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        }
                    ],
                    downloaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyFile
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        }
                    ]
                },
                MESSAGE: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        }
                    ]
                },
                RESTRICTED: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        }
                    ]
                },
                MEETINGCARD: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: info => {
                                let schedule = cloneDeep(F.paths(info, 'plainMsg', 'meta') || {});
                                schedule.isCrystalSdk = checkConfIdSdk(schedule.conferenceID);
                                if (schedule.isCrystalSdk && schedule.conferenceType === '2') {
                                    schedule.cycleParams = transformCstCycleParams(schedule.cycleParams);
                                }
                                copyInvitation(schedule, this.$t('right_content_panel.copySuccess'));
                            }
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_join_meeting'),
                            type: 'join_meeting',
                            handler: info => {
                                const schedule = F.paths(info, 'plainMsg', 'meta') || {};

                                new MeetingService({
                                    meetingEventType: joinMeetingEnum.Card_join
                                }).linkJoinMeeting({
                                    url: schedule.joinUrl
                                });
                            }
                        }
                    ]
                },
                MSGCARD: {
                    default: []
                }
            };
            return filterOptions(options);
        },
        // 右键弹出选项类型 最后一个为删除类型，需注意！
        RIGHT_CLICK_OPTIONS() {
            const options = {
                UNKNOW: [
                    {
                        label: this.$t('right_content_panel.right_click_panel_reply'),
                        type: 'reply',
                        handler: this.replyHandler
                    }
                ],
                DEFAULT: [
                    {
                        label: this.$t('right_content_panel.right_click_panel_reply'),
                        type: 'reply',
                        handler: this.replyHandler
                    },
                    {type: 'driver'},
                    {
                        label: this.$t('right_content_panel.right_click_panel_copy'),
                        type: 'copy',
                        handler: this.copyMessageHandler
                    },
                    {
                        label: this.$t('right_content_panel.right_click_panel_forward'),
                        type: 'forward',
                        handler: this.forwardTextMessage
                    },
                    {
                        label: this.$t('right_content_panel.right_click_panel_pin'),
                        type: 'pin',
                        handler: this.groupMessagePinOption
                    },
                    {
                        label: this.$t('right_content_panel.right_click_panel_unpin'),
                        type: 'unpin',
                        handler: this.groupMessagePinOption
                    },
                    {
                        label: this.$t('right_content_panel.right_click_panel_select'),
                        type: 'select',
                        handler: this.selectTextMessage
                    },
                    {
                        label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                        type: 'add_emoji',
                        handler: () => {}
                    },
                    {type: 'driver'},
                    {
                        label: this.$t('right_content_panel.right_click_panel_delete'),
                        type: 'delete',
                        handler: this.deleteTextMessage
                    }
                ],
                E2EDESCRIPT: {
                    default: [
                        // {
                        //   label: this.$t("right_content_panel.right_click_panel_reply"),
                        //   type: 'reply',
                        //   handler: this.replyHandler
                        // },
                        // {
                        //   label: this.$t("right_content_panel.right_click_panel_delete"),
                        //   type: 'delete',
                        //   handler: this.deleteTextMessage
                        // }
                    ]
                },
                VCARD: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                VOICE: {
                    default: this.isAudioForward
                        ? [
                              {
                                  label: this.$t('right_content_panel.right_click_panel_reply'),
                                  type: 'reply',
                                  handler: this.replyHandler
                              },
                              {type: 'driver'},
                              {
                                  label: this.$t('right_content_panel.right_click_panel_forward'),
                                  type: 'forward',
                                  handler: this.forwardTextMessage
                              },
                              {
                                  label: this.$t('right_content_panel.right_click_panel_select'),
                                  type: 'select',
                                  handler: this.selectTextMessage
                              },
                              {
                                  label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                                  type: 'add_emoji',
                                  handler: () => {}
                              },
                              {type: 'driver'},
                              {
                                  label: this.$t('right_content_panel.right_click_panel_delete'),
                                  type: 'delete',
                                  handler: this.deleteTextMessage
                              }
                          ]
                        : [
                              {
                                  label: this.$t('right_content_panel.right_click_panel_reply'),
                                  type: 'reply',
                                  handler: this.replyHandler
                              },
                              {
                                  label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                                  type: 'add_emoji',
                                  handler: () => {}
                              },
                              {type: 'driver'},
                              {
                                  label: this.$t('right_content_panel.right_click_panel_delete'),
                                  type: 'delete',
                                  handler: this.deleteTextMessage
                              }
                          ]
                },
                MEETINGINVITE: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMeetingInviteMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ]
                },
                CALLRECORD: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ]
                },
                DOCUMENT: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_download'),
                            type: 'download',
                            handler: this.$refs[this.currentChatInfo.uuid]?.downloadHandle
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    downloading: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_cancel'),
                            type: 'cancel',
                            handler: this.$refs[this.currentChatInfo.uuid]?.cancelHandle
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    downloadPause: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_download'),
                            type: 'download',
                            handler: this.$refs[this.currentChatInfo.uuid]?.documentOperationHandle
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    downloaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_open_file_location'),
                            type: 'open_file_location',
                            handler: this.openFileLocationMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    uploading: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_cancel'),
                            type: 'cancel',
                            handler: this.$refs[this.currentChatInfo.uuid]?.cancelHandle
                        }
                    ],
                    uploadPause: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_retry'),
                            type: 'upload',
                            handler: this.$refs[this.currentChatInfo.uuid]?.resetUploadHandle
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    uploaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_open_file_location'),
                            type: 'open_file_location',
                            handler: this.openFileLocationMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ]
                },
                Announcement: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_Comment'),
                            type: 'comment',
                            handler: this.commentHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                AnnouncementCommit: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_Comment'),
                            type: 'comment',
                            handler: this.commentHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                PICTURE: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_cancel'),
                            type: 'cancel',
                            handler: this.$refs[this.currentChatInfo.uuid]?.cancelHandle
                        }
                    ],
                    download: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        }
                    ],
                    uploaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyFile
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    uploading: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_cancel'),
                            type: 'cancel',
                            handler: this.$refs[this.currentChatInfo.uuid]?.cancelHandle
                        }
                        // {
                        //   label: 'Forward',
                        //   handler: this.forwardDocumentMessage
                        // },
                        // {
                        //   label: this.$t("right_content_panel.right_click_panel_delete"),
                        //   handler:this.deleteDocumentMessage
                        // }
                    ],
                    downloaded: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyFile
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_save'),
                            type: 'save',
                            handler: this.savePictureMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardDocumentMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ],
                    uploadPause: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_retry'),
                            type: 'upload',
                            handler: this.$refs[this.currentChatInfo.uuid]?.resetUploadHandle
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteDocumentMessage
                        }
                    ]
                },
                RESTRICTED: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                MESSAGE: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_edit'),
                            type: 'edit',
                            handler: this.editTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_translate'),
                            type: 'translate',
                            handler: this.translateTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_hide_translate'),
                            type: 'hide_translate',
                            handler: this.hideTranslateTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_retry'),
                            type: 'retry',
                            handler: this.retrySendMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                RobotRich: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        }
                    ]
                },
                ForRobotRich: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                MESSAGELONG: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_edit'),
                            type: 'edit',
                            handler: this.editTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_translate'),
                            type: 'translate',
                            handler: this.translateTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_hide_translate'),
                            type: 'hide_translate',
                            handler: this.hideTranslateTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_retry'),
                            type: 'retry',
                            handler: this.retrySendMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                MEETINGCARD: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: info => {
                                let schedule = cloneDeep(F.paths(info, 'plainMsg', 'meta') || {});
                                schedule.isCrystalSdk = checkConfIdSdk(schedule.conferenceID);
                                if (schedule.isCrystalSdk && schedule.conferenceType === '2') {
                                    schedule.cycleParams = transformCstCycleParams(schedule.cycleParams);
                                }
                                copyInvitation(schedule, this.$t('right_content_panel.copySuccess'));
                            }
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_join_meeting'),
                            type: 'join_meeting',
                            handler: info => {
                                const schedule = F.paths(info, 'plainMsg', 'meta') || {};

                                new MeetingService({
                                    meetingEventType: joinMeetingEnum.Card_join
                                }).linkJoinMeeting({
                                    url: schedule.joinUrl
                                });
                            }
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteMeetCardMessage
                        }
                    ]
                },
                APPROVALCARD: {
                    default: []
                },
                APPROVALFORWARDCARD: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                RICHTEXT: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_copy'),
                            type: 'copy',
                            handler: this.copyMessageHandler
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_translate'),
                            type: 'translate',
                            handler: this.translateTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_hide_translate'),
                            type: 'hide_translate',
                            handler: this.hideTranslateTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                CombineText: {
                    default: [
                        {
                            label: this.$t('right_content_panel.right_click_panel_reply'),
                            type: 'reply',
                            handler: this.replyHandler
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_forward'),
                            type: 'forward',
                            handler: this.forwardTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_pin'),
                            type: 'pin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_unpin'),
                            type: 'unpin',
                            handler: this.groupMessagePinOption
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_select'),
                            type: 'select',
                            handler: this.selectTextMessage
                        },
                        {
                            label: this.$t('right_content_panel.right_click_panel_add_emoji'),
                            type: 'add_emoji',
                            handler: () => {}
                        },
                        {type: 'driver'},
                        {
                            label: this.$t('right_content_panel.right_click_panel_delete'),
                            type: 'delete',
                            handler: this.deleteTextMessage
                        }
                    ]
                },
                UNSUPPORTED: {
                    default: []
                }
            };
            return filterOptions(options);
        },

        selectTextMessage(info) {
            fireMessageSelect();
            this.$store.commit('uiControl/setMultipleSelected', true);
            // Update view reset selected message
            this.viewChatList = (this.viewChatList || []).map(item => ({
                ...item,
                // If it is not set, then re-get set
                isNotSupportForward: !this.isSupportedForwards(item),
                isMultipleSelectChecked: info.uuid === item.uuid,
                isMultipleSelectDisabled: false
            }));

            if (this.editMessageState?.uuid) {
                this.editMessageState.uuid = '';
            }
        },
        openPoiWin(info) {
            const meta = info.plainMsg?.m?.meta;

            if (meta) {
                const query = {
                    lat: meta.poiLatitude,
                    lng: meta.poiLongitude,
                    poiName: meta.poiName,
                    poiAddress: meta.poiAddress
                };
                ipcRenderer.send('map-window-main', {
                    type: 'show',
                    args: {
                        ...query
                    }
                });
            }
        }
    }
};
