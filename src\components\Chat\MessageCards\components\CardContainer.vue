<template>
  <div>

  </div>
</template>

<script>
  export default {
    name: 'CardContainer',
    data () {
      return {
        containerData: {
          "tag": "column_set", // 分栏的标签。
          "horizontal_spacing": "8", // 分栏中列容器之间的间距。默认值 default（为 8px）。
          "horizontal_align": "left", // 列容器水平对齐的方式。默认值 left。
          "margin": "0", // 列容器的外边距。
          "flex_mode": "none", // 移动端和 PC 端的窄屏幕下，各列的自适应方式。默认值 none。
          "background_style": "default", // 分栏的背景色样式。默认值 default。
          "action": { // 在此处设置点击分栏时的交互配置。
            "multi_url": {
              "url": "https://open.feishu.cn",
              "pc_url": "https://open.feishu.com",
              "ios_url": "https://developer.apple.com/",
              "android_url": "https://developer.android.com/"
            }
          },
          "columns": [
            // 列配置
            {
              "tag": "column",
              "background_style": "default", // 列的背景色样式。默认值 default。
              //"width": "auto", // 列容器的宽度。默认值 auto。
              "weight": 1, // 当 width 取值 weighted 时生效，表示当前列的宽度占比。
              "vertical_align": "center", // 列垂直居中的方式。
              "vertical_spacing": "4px", // 列内子组件纵向间距。默认值 default（8px）。
              "padding": "8", // 列容器的内边距。默认值 0px。
              "action": {
                // 在此处设置点击列时的交互配置。
                "multi_url": {
                  "url": "https://www.baidu.com",
                  "pc_url": "https://www.baidu.com",
                  "ios_url": "https://www.google.com",
                  "android_url": "https://www.apple.com.cn"
                }
              },
              "elements": [] // 列容器内嵌的组件，不支持内嵌表格组件、多图混排组件和表单容器。
            }
          ]
        }
        // 2等分、3等分、None按权重
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>