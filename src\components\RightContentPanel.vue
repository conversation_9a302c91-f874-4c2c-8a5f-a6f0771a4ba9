<template>
  <div class="message-box dialog-content"
       id="rightContentPannelDom">
    <el-dialog custom-class="group-unpin-dialog"
               :title="$t('right_content_panel.groupPin.unPinTitle')"
               :visible.sync="$store.state.uiControl.groupUnPinDialogVisible"
               :before-close="groupUnpinDialogHandleClose"
               :close-on-click-modal="false"
               top="40vh"
               append-to-body>
      <div class="content">
        <div class="body">{{$t('right_content_panel.groupPin.unPinContent')}}</div>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button class="cancelBtn"
                   @click="groupUnpinDialogHandleClose ">{{$t('right_content_panel.groupPin.unPinCancelBtn')}}</el-button>
        <el-button type="primary"
                   @click="groupPinAction">{{$t('right_content_panel.groupPin.unPinBtn')}}</el-button>
      </span>
    </el-dialog>

    <!-- 消息提示 -->
    <SendFileDialog :isBeBlockInE2EE="isBeBlockInE2EE && isFriend"
                    @dialogMessageHandle="mockSendMsgHandle"
                    ref="sendFileRef"></SendFileDialog>
    <div class="above_box">
    </div>
    <!-- 联系人卡片 -->
    <ConcatPopover :visible.sync="contactCard.visible"
                   ref="ConcatPopover"
                   popper-class="popover-contact"
                   trigger="click">
      <contact-card @chat="chatNow"
                    @add="addToContact"
                    @meet="meetSinglePure"
                    :who="contactCard.who"
                    :peer="contactCard.peer" />
    </ConcatPopover>
    <!-- 已读卡片 -->
    <RecipientCard ref="RecipientCard"
                   v-model="recipientCardObj.showValue"
                   :pageY="recipientCardObj.pageY"
                   :pageX="recipientCardObj.pageX"
                   :offsetY="recipientCardObj.offsetY"
                   :groupUserId="recipientCardObj.groupUserId"
                   :groupPeerInfo="recipientCardObj.groupPeerInfo"
                   :readmembers="recipientCardObj.readmembers"
                   :unreadmembers="recipientCardObj.unreadmembers"
                   :readNum="recipientCardObj.readNum"
                   :unreadNum="recipientCardObj.unreadNum"
                   @callback="showContactInfoByRecipientCard"></RecipientCard>
    <ChatHead @meetingInvite="sendMeetingInvite"
              :isE2EFaile="isE2EFaile"
              ref="chatHeadRef"></ChatHead>
    <span class="grey-5 caption-1"
          style="position: absolute; z-index: 1; left: 24px; top: 4px; user-select: text;"
          v-if="isDebug">{{actDialogUid}} | {{actDialogId}} | {{mySession?.historyMsgMaxSeq}} | {{mySession?.maxSeq}}</span>
    <TimeTip :timestamp="recentTime"></TimeTip>
    <div class="view-scroll-wrap"
         :class="{isMultipleSelected}">
      <ViewScroll ref="container_view"
                  class="chatscroll-container container_view"
                  :scrollList="viewChatListComputed"
                  :metionList="metionedList"
                  :isFetching="isFetchMessage"
                  :distance="6"
                  @scroll="throttleScroll"
                  @scrollToTop="scrollTopLoadMore"
                  @loadMore="scrollBottomLoadMore"
                  @scrollHandle="scrollHandle"
                  :isInRightContent="true">
        <template v-slot:content="chatItemProp">
          <section class="chatmessage-container">
            <!--TODO Dev Test -->
            <MessageCard />
            <section>
              <component :is="'dd'"
                         :id="chatItemProp.chatItem.uuid"
                         v-bind="chatTypeComputed(chatItemProp.chatItem, isSubscribe)"
                         v-time-change="{
                    chatItem:chatItemProp.chatItem.timestamp
                  }"
                         :ts="chatItemProp.chatItem.timestamp"
                         class="menu"
                         @rightClickMessage="openRightPanel"
                         @sendMeetingCardReply="sendMeetingCardReplyMsg"
                         :ref="chatItemProp.chatItem.uuid"
                         @viewAtClick="viewAtClick"
                         @reSendMessage="reSendMessage"></component>

              <RightMessageEdit v-if="editMessageState.uuid === chatItemProp.chatItem.uuid"
                                ref="rightMessageEdit"
                                :chatInfo="chatItemProp.chatItem"
                                :currentInputHeight="currentInputHeight"
                                @editSend="onMessageEditSend"
                                @editClear="onMessageEditClear"
                                @metion="onMessageEditMetion" />

              <p v-if="isDebug && chatItemProp && chatItemProp.chatItem && chatItemProp.chatItem.plainMsg"
                 class="grey-5 caption-1"
                 :class="{'red-4': chatItemProp.chatItem.lastSeq && viewChatListComputed[chatItemProp.cIdx - 1] && viewChatListComputed[chatItemProp.cIdx - 1].msgSeq && viewChatListComputed[chatItemProp.cIdx - 1].msgSeq !== chatItemProp.chatItem.lastSeq}"
                 style="text-align: left;margin-left: 70px;">
                ⭡ msgSeq: {{chatItemProp.chatItem.msgSeq}}
                | lastSeq: {{chatItemProp.chatItem.lastSeq}}
                | stime: {{chatItemProp.chatItem.stime}} | uuid: {{chatItemProp.chatItem.uuid}}
                | f: {{chatItemProp.chatItem.plainMsg.f}} | t: {{chatItemProp.chatItem.plainMsg.t}} | s: {{chatItemProp.chatItem.plainMsg.s}}
              </p>
            </section>
          </section>
        </template>
      </ViewScroll>
      <ReplyListPannel>
        <template v-slot:content="chatItemProp">
          <section class="chatmessage-container">
            <section>
              <component :is="'dd'"
                         :id="chatItemProp.chatItem.uuid"
                         v-bind="chatTypeComputed(chatItemProp.chatItem, isSubscribe)"
                         v-time-change="{
                      chatItem:chatItemProp.chatItem.timestamp
                    }"
                         :ts="chatItemProp.chatItem.timestamp"
                         class="menu"
                         :groupPinVisable="true"
                         @viewAtClick="viewAtClick"
                         @rightClickMessage="openRightPanel"></component>
            </section>
          </section>
        </template>
      </ReplyListPannel>
      <GroupPinPannel>
        <template v-slot:content="chatItemProp">
          <section class="chatmessage-container">
            <section>
              <component :is="'dd'"
                         :id="chatItemProp.chatItem.uuid"
                         v-bind="chatTypeComputed(chatItemProp.chatItem, isSubscribe)"
                         v-time-change="{
                      chatItem:chatItemProp.chatItem.timestamp
                    }"
                         :ts="chatItemProp.chatItem.timestamp"
                         class="menu"
                         :groupPinVisable="chatItemProp.groupPinVisable"
                         :isGroupPinHover="chatItemProp.isGroupPinHover"
                         @viewAtClick="viewAtClick"
                         @rightClickMessage="openRightPanel"></component>
            </section>
          </section>
        </template>
      </GroupPinPannel>
    </div>
    <RightClickOption :tranLeft="tranLeft"
                      :tranTop="tranTop"
                      :tranRight="tranRight"
                      :show="chatContextMenuVisible"
                      :options="rightPanelOptions"
                      :chatInfo="currentChatInfo"
                      @select-emoji="emojiSelectForRightOption"
                      ref="rightClickOption" />

    <div v-if="!isSubscribe"
         v-show="!isMultipleSelected"
         class="message-sendbox-container">
      <PanelSplit @drag-split="reSizeInput"
                  :currentHeight="currentInputHeight" />
      <div class="message-sendboxwrap message-sendboxwrap--faile"
           v-show="isE2EFaile"
           @click="faileE2ESessionHandle"></div>
      <reply-pannel :replyPannelVisible="currentDialogReplyInfo.replyPannelVisible"
                    @replyPannelClose="clearReplyPannel"
                    :replyChatInfo="currentDialogReplyInfo.replyChatInfo">
      </reply-pannel>
      <div v-if="isBeBlockInE2EE&&isFriend"
           class="message-sendboxwrap blocked-message-sendboxwrap">
        <div><span class="unblock-span"
                @click="unBlock">{{$t('right_content_panel.unblock_button')}}</span> {{$t('right_content_panel.block_text')}}</div>
      </div>
      <div v-else-if="!isExistFromDBMemberInCurrentSpace&&contactsInitDone"
           class="message-sendboxwrap message-box-remove">
        <div class="person-removed">{{$t('right_content_panel.person_removed')}}</div>
      </div>
      <div v-else
           class="message-sendboxwrap"
           @click.self="messageInputFocus"
           :style="{'height':currentInputHeight+'px'}"
           ref="sendInputRef">
        <div class="action-icon-group">
          <Tooltip effect="dark"
                   popper-class="chats_-tooltip"
                   :content="$t('ChatDialog.Emoji')"
                   :hide-after="0"
                   :visible-arrow="false"
                   placement="bottom">
            <div class="chat_icon emoji_icon"
                 ref="emojiIcon"
                 @click.stop="emojiMouseEnterHandle($event)">
            </div>
          </Tooltip>
          <input type="file"
                 multiple="multiple"
                 class="inputStyle"
                 id="upimage"
                 name="upimage"
                 accept="image/*, .mp4,.m4v,.mov"
                 @change="fileChangeHandle_($event, 'imageInput')"
                 ref="imageInput">
          <label for="upimage"
                 v-if="!imageLimitInfo"
                 class="chat_icon upimage">
            <Tooltip effect="dark"
                     popper-class="chats_-tooltip"
                     :content="$t('settingsUtil.Medias')"
                     :hide-after="0"
                     :visible-arrow="false"
                     placement="bottom">
              <div class="chat_icon flex-center-center imageIcon_icon"
                   @click.stop="fileClickHandle">
                <SvgIcon iconClass="icon_chat_image"
                         size="18" />
              </div>
            </Tooltip>
          </label>
          <input type="file"
                 multiple="multiple"
                 class="inputStyle"
                 id="upimfile"
                 name="upimfile"
                 :accept="fileAcceptValue"
                 @change="fileChangeHandle_($event, 'fileInput')"
                 ref="fileInput">
          <label for="upimfile"
                 v-if="!fileAcceptInfo"
                 class="chat_icon upimfile">
            <Tooltip effect="dark"
                     popper-class="chats_-tooltip"
                     :content="$t('ChatDialog.File')"
                     :hide-after="0"
                     :visible-arrow="false"
                     placement="bottom">
              <div class="chat_icon fileIcon_icon"
                   @click.stop="fileClickHandle">
              </div>
            </Tooltip>
          </label>
          <Tooltip effect="dark"
                   popper-class="chats_-tooltip"
                   :content="$t('ChatDialog.Contact')"
                   :hide-after="0"
                   :visible-arrow="false"
                   v-if="isSendCard"
                   placement="bottom">
            <div class="chat_icon namecard_icon"
                 @click.stop="nameCardHandle">
            </div>
          </Tooltip>
          <Tooltip effect="dark"
                   v-if="isGroup"
                   popper-class="chats_-tooltip"
                   :content="$t('ChatDialog.Mention')"
                   :hide-after="0"
                   :visible-arrow="false"
                   placement="bottom">
            <div class="chat_icon mention_icon"
                 @click.stop="addMentionHandle">
            </div>
          </Tooltip>
          <div v-if="isCapture"
               class="chat_icon screen_capture_wrap">
            <el-popover @show="onShowPopover"
                        placement="bottom-start"
                        :title="`${$t('right_content_panel.screenshot_title')} ${shortcutstr}`"
                        trigger="hover"
                        :visible-arrow="false"
                        :close-delay="0"
                        transition="none"
                        popper-class="popover-screen-capture">
              <div slot="reference"
                   class="screen-capture"
                   @click="startScreenCapture"></div>
            </el-popover>
            <el-popover v-model="visibleHidePopover"
                        :placement="$i18n.locale === 'ar' ? 'bottom-end': 'bottom-start'"
                        trigger="click"
                        :visible-arrow="false"
                        :close-delay="0"
                        transition="none"
                        popper-class="popover-screen-capture down-icon">
              <template slot#content>
                <div class="form-wrap"
                     :class="{'is-checked': isHideMainWindow}"
                     @click="visibleHidePopover = false">
                  <el-checkbox class="is-dark"
                               v-model="isHideMainWindow"
                               @change="setHideMainWindow">{{$t('right_content_panel.screenshot_description')}}</el-checkbox>
                </div>
              </template>
              <div slot="reference"
                   class="down-icon"></div>
            </el-popover>
          </div>
        </div>
        <MessageInput v-model="willSendMsg"
                      class="message-sendbox"
                      :placeholder="$t('right_content_panel.sendTxtPlaceHolder')"
                      :peerId="actDialogId"
                      :grouplist="grouplist"
                      :groupInfo="groupInfo"
                      :emojiIconRef="$refs.emojiIcon"
                      ref="messageInputer"
                      :isNeedSuggestion="true"
                      @clipboardData="clipboardData"
                      @sendMsg="sendMsgWithVerif"
                      @sendLongMsg="sendLongMsg"
                      @resizeHeight="reSizeInput"
                      @suggestSelect="onSuggestSelect"
                      :currentInputHeight="currentInputHeight"
                      :backTargetOptions="backTargetOptions"></MessageInput>
        <!-- <Icon :value=" ? 'send_msg_active' : 'send_msg'"

              ></Icon> -->
        <div class="message-sendbox-icon"
             :class="{active: this.$refs.messageInputer && this.$refs.messageInputer.inputValue.trim()}"
             @click="emitSendMsg($refs.messageInputer)">
          <SvgIcon iconClass="ic_send_msg" />
        </div>
      </div>
    </div>
    <div v-else>
      <textarea type="textarea"
                ref="message-hidden-input"
                class="message-hidden-input"
                style="width: 0; height:0;"
                @keydown.down="onKeydownborad('next', $event)"
                @keydown.up="onKeydownborad('prev', $event)"></textarea>
    </div>
    <div v-if="isMultipleSelected"
         class="select-following">
      <div class="select-box"
           @click="selectFollowingMessage">{{$t('CombineText.SelectFollowingMessages')}}</div>
    </div>
    <div v-if="isMultipleSelected"
         style="position: relative;">
      <div class="suggestion-backtop">
        <Backtarget class="backtop-btn"
                    :scrollToMetioned="backTargetOptions.scrollToMetioned"
                    :scrollNewMessage="backTargetOptions.scrollNewMessage"
                    :scrollBottomNew="backTargetOptions.scrollBottomNew"
                    :metionedList="backTargetOptions.metionedList"
                    :showMetion="backTargetOptions.showMetion"
                    :visibleArrowUp="backTargetOptions.visibleArrowUp"
                    :newMessagesNumer="backTargetOptions.newMessagesNumer"
                    :candown="backTargetOptions.candown"
                    :badgeCount="backTargetOptions.badgeCount"></Backtarget>
      </div>
      <div class="select-footer-bar flex-between-center">
        <ul class="flex-around-center">
          <li v-if="isSaveMultiMedia"
              class="select-share-item"
              @click="onBatchSaveSelectFile">
            <div class="select-item-icon flex-center-center">
              <SvgIcon size="16"
                       iconClass="ic_file_download" />
            </div>
            <p class="grey-8 caption-1">{{$t('selectedPannel.SaveFiles')}}</p>
          </li>
          <li class="select-share-item"
              @click="onSelectCombineButton">
            <div class="select-item-icon flex-center-center">

              <SvgIcon size="16"
                       iconClass="ic_forward_combine" />
            </div>
            <p class="grey-8 caption-1">{{$t('selectedPannel.CombineForward')}}</p>
          </li>
          <li class="select-share-item"
              @click="onSelectShareButton">
            <div class="select-item-icon flex-center-center">

              <SvgIcon size="16"
                       iconClass="ic_forward" />
            </div>
            <p class="grey-8 caption-1">{{$t('selectedPannel.OneByOneForward')}}</p>
          </li>
        </ul>
        <SvgIcon iconClass="ic_close"
                 size="24"
                 @click="onSelectShareClose" />
      </div>
    </div>
    <E2EVerifyCode></E2EVerifyCode>
    <FeedbackDialog />
  </div>
</template>

<script>
// import Icon from '@/components/kits/Icon.vue';
import TimeTip from '@/components/TimeTip.vue';
import ViewDialog from '@/components/Chat/ViewDialog';
import PreSendFile from '@/components/Chat/PreSendFile';
import PanelSplit from '@/components/common/PanelSplit';
import RightClickOption from '@/components/Panel/RightClickOption';
import Portrait from '@/components/Portrait/Portrait';
import TimeChat from '@/components/TimeChat.vue';
import E2ETips from '@/components/Chat/E2ETips.vue';
import E2EKeyChangeTip from '@/components/Chat/E2EKeyChangeTip.vue';
import E2EDescript from '@/components/Chat/E2EDescript.vue';
import ContactCard from '@/components/kits/ContactCard.vue';
import NameCard from '@/components/Chat/NameCard.vue';
import VoiceChat from '@/components/Chat/VoiceChat/VoiceChat.vue';
import FloatTip from '@/components/FloatTip.vue';
import PictureChat from '@/components/Chat/PictureChat.vue';
import EventChat from '@/components/Chat/EventChat.vue';
import MessageTxtChat from '@/components/Chat/MessageTxtChat.vue';
import AnnouncementChat from '@/components/Chat/AnnouncementChat.vue';
import CombineChat from '@/components/Chat/CombineChat.vue';
// import RobotTextChat from '@/components/Chat/RobotTextChat.vue';
import RobotRichChat from '@/components/Chat/RobotRichChat.vue';
import ForRobotRichChat from '@/components/Chat/ForRobotRichChat.vue';
import MessageTxtRichChat from '@/components/Chat/MessageTxtRichChat.jsx';
import messageRecept from '@/components/Chat/messageRecept.vue';
import DocumentChat from '@/components/Chat/DocumentChat.vue';
import MeetingInviteChat from '@/components/Chat/MeetingInviteChat.vue';
import CallRecordChat from '@/components/Chat/CallRecordChat.vue';
import E2EVerifyCode from '@/components/Chat/E2EVerifyCode.vue';
import MessageCard from '@/components/Chat/MessageCards/MessageCard.vue';
import MeetingCard from '@/components/Chat/MeetingCard.vue';
import ApprovalCard from '@/components/Chat/ApprovalCard.vue';
import ApprovalForwardCard from '@/components/Chat/ApprovalForwardCard.vue';
import UnknowChat from '@/components/Chat/UnknowChat.vue';
import UnsupportedChat from '@/components/Chat/UnsupportedChat.vue';
import MapView from '@/components/Chat/MapView.vue';
import MessageComponents from '@/components/Chat/MessageComponents/index.vue';
import RightMessageEdit from './RightMessageEdit.vue';

import E2EEPingUtil from '@/utils/E2EEPingUtil';
import {getSpaceId} from '@/utils/SpaceManager';
import * as Peer from '@/api/peerApi';
import _ from 'lodash';
import Bus from '@/utils/vueBus';
const {decodeMessageTransform} = require('@/utils/dataDao');
import Nickname from '@/components/Nickname/Nickname.vue';

import RecipientCard from '@/components/settings/util/RecipientCard';

import ReplyPannel from '@/components/Chat/ReplyPannel';
import MessageInput from '@/components/MessageInput/MessageInput';
import ViewScroll from '@/components/ViewScroll';
import ReplyListPannel from '@/components/ReplyListPannel';
import GroupPinPannel from '@/components/GroupPin/GroupPinPannel';
import ChatHead from '@/components/ChatHead';
import SendFileDialog from '@/components/Chat/SendFileDialog';
import {MsgTemplate, handleMessage, editMessageHandle, replaceMessageByEditText, preprocess} from '@/api/messageManger.js';
import timeChange from '@/directive/time-change';
// const fileIcon = require('@/assets/chat/msgInput/ic_message_file.svg');
// const fileIconActive = require('@/assets/icons/ic_message_file_p.svg');
// const emojiIcon = require('@/assets/icons/ic_emoji_btn.svg');
// const emojiIconActive = require('@/assets/icons/ic_emoji_btn_hover.svg');
// const sendIconActive = require('@/assets/icons/<EMAIL>');

import {mapState, mapActions, mapMutations, mapGetters} from 'vuex';
import {isScrollBarToBottom} from '@/utils/dom.js';
import {encodeForMetion, forceAddEqForHid} from '@/utils/Function.js';
import {dataUtil} from '@/utils/dataUtil';
import * as F from '@/utils/Function';
import {keepLastIndex} from '@/utils/index';
import renderMsgItem, {msgRestrictedTypeInfo} from '@/utils/renderMsgItem';
import {getMessageWithDatabase, sendPinMsg, deleteMessage} from '@/api/messageApi';
import * as Caught from '@/utils/caughtError';
import {getContactSimpleInfoList, getEnterpriseMember} from '@/api/peerApi';
import {setClipboard} from '@/utils/clipboard.js';
import {ipcRenderer} from 'electron';
import {enCodeSpaceHid, defaultSpaceId} from '@/dataController/hid';
import {personalSpaceId} from '../dataController/hid';
import {startCallMeeting} from '@/utils/sdk/meetingUtils';
import msgDevLog from '@/logs/msgDevLog';
import * as ConstantTypes from '@/utils/ConstantTypes';
import {copyMetaInvitation} from '@/utils/meeting';
import {getShortcut} from '@/components/settings/util/ShortcutSettings/shortcut';
import rightOptionsMix from '@/mixins/rightOptionsMix';
import chatTranslateMix from '@/mixins/chatTranslateMix';
import {getWith} from '@/utils/SqliteUtil';
import devFileLog from '@/logs/devFileLog';
import {getHideMainWindow, setHideMainWindow} from '@/components/settings/util/ShortcutSettings/shortcut';
import Backtarget from '@/components/Chat/Backtarget';
import QueueManager from '@/utils/QueueManager';
import {sendPictureViewerWithdraw} from '@/renderer/pictureViewer/utils';
import {getGroupUidList} from '@/api/peerApi';
import {
    // watchFocusViewReceipt,
    sendMsgReceiptInfo,
    scrollToMetionedReceipt,
    scrollBottomNewReceipt,
    scrollNewMessageReceipt
} from '@/utils/chat/receiptUtils';
import {getTimestamp} from '@/utils/timestampFix';
import {getDBVoiceIsListened} from '@/api/messageApi';
import {fireNameCardOpen, fireReceiptsListChat, fireMsgUnpined, fireMsgPined} from '@/Firebase';
import {checkImageFileType, validationFileExtname, validationFileCludesImage} from '@/utils/fileType';
import {existFile} from '@/utils/FileTool';
import {sendSdk} from '@/utils/ipc/ipcSend.js';
// import {clearMentioned} from '@/api/sessionApi';
import ConcatPopover from '@/components/settings/util/ConcatPopover';
import {mangePeerList} from '@/utils/doPolymericPeer';
import {v4 as uuidv4} from 'uuid';
import {createQueue} from '@/utils/queue/use-queue.js';
import {checkPathPermitted} from '@/utils/files/downloadPath.js';
import {checkUserDiskFreeSpace} from '@/utils/files/checkDisk.js';
import {deleteTempMessage} from '@/utils/message/destroyMsg/destroyMessage.js';
import {userMessage} from '@/api/userApi.js';
import {forceCloseHandle} from '@/api/forceCloseHandle';
import {FtsService} from '@/utils/ftsDB/ftsService.js';
import {SearchEnum} from '@/enum/SearchStatus.js';
import FeedbackDialog from '@/components/Chat/FeedbackDialog.vue';
import {renderName} from '@/utils/index';
import {getPeerDescribe} from '@/utils/peer/peetUtils';
import {subscribePeerStatus, unsubscribePeerStatus} from '@/api/peerStatusApi';
import * as Tool from '@/utils/FileTool';
import BatchFileSaver from '@/utils/files/BatchFileSaver';
import {downloadMinDiskSize, downloadDiskSize} from '@/main/constants.js';
import {parseMarkdownToPlainText} from '@/utils/editor/parse.js';
import {regDebugMessage, batchSendMsgHandle} from '@/utils/message/batchSendMsg.js';
import {fetchDialogHistoryMsgs} from '@/utils/message/historyMsg.js';
import {getRegions, checkDialogMessage} from '@/utils/message/msgSeqUtils';
import {fetchCurrentDialogOfflineMsg} from '@/utils/message/offlineMsg.js';
import {fetchOfflineMessageHighPriorityTask} from '@/utils/message/offlineMessageTask.js';
import {isDebug} from '@/config/config';
// import {conversationGetMaxId} from '@/api/conversationApi';

const CHAT_TIME_INTERVAL = 1000 * 60 * 5;
function renderE2ETemplate() {
    return {
        customData: {
            msg: 'E2ETIPS'
        },
        direct: 'LEFT',
        token: '',
        chatComponentType: 'E2ETIPS',
        timestamp: getTimestamp(),
        uuid: 123 - 123 - 123,
        isShowTimeStamp: false,
        plainMsg: {},
        stime: getTimestamp(),
        peerId: '',
        keyChange: false
    };
}
function renderUserSelfTemplate() {
    return {
        customData: {
            msg: 'Messages sent here can be received on Matrx mobile terminal'
        },
        direct: 'LEFT',
        token: '',
        chatComponentType: 'EVENT',
        timestamp: getTimestamp(),
        uuid: 123 - 123 - 121,
        isShowTimeStamp: false,
        plainMsg: {},
        stime: getTimestamp(),
        peerId: '',
        keyChange: false
    };
}
// 模拟发送消息
async function productMsg(file) {
    let sendTo = this.$store.state.uiControl.actDialogId;
    let msgTemplate = new MsgTemplate();
    let msgBody = await msgTemplate.getFileTemplate(sendTo, file);
    return msgTemplate.setList(JSON.stringify(msgBody));
}
// 排除其他消息如recept回执
function filterText(list) {
    if (list && list.length > 0) {
        return list.filter(val => {
            return !val.match(/\|/g) || (val.match(/\|/g) && val.match(/\|/g).length < 2);
        });
    } else {
        return [];
    }
}

let statusOfLoading = false;
let stautsOfnewloading = false;

let newMsgQueue = null;

async function haveReadNewMessages(div, keepvm) {
    if (keepvm.visibleArrowUp == false) return;
    let el = document.getElementById(keepvm.newMessagesUUID);
    if (el) {
        if (F.isBehindViewportTop(el, div)) {
            keepvm.visibleArrowUp = false;
            keepvm.newMessagesNumer = 0;
            keepvm.newMessagesUUID = null;
        }
    } else {
        return;
    }
}
function flashingHook(uuid) {
    F.metionAnimation(document.getElementById(uuid));
}
function buildMessageMetioned(timer = null) {
    let unreadList = [];
    let keepvm = null;
    let start = async () => {
        if (unreadList.length == 0) {
            return;
        } else {
            let [head, element] = unreadList.shift();
            let spaceId = defaultSpaceId();
            await Peer.saveMentioned(keepvm.$store.state.uiControl.actDialogId, head, spaceId, true);
            await keepvm.$store.dispatch('uiControl/refreshMetioned');
            let key = 'newmsg__' + keepvm.$store.state.uiControl.actDialogId;
            if (window[key]) window[key]();
            // 定位到该位置
            F.metionAnimation(element);
            await start();
        }
    };

    let build = div => () => {
        document.querySelectorAll('[metion=true]').forEach(e => {
            if (F.isInOfViewport(e, div)) {
                let id = e.getAttribute('id');
                if (unreadList.map(a => a[0]).indexOf(id) == -1) {
                    unreadList.push([id, e]);
                }
            }
        });
        start();
        // have read New Messages logic
        haveReadNewMessages(div, keepvm);
    };
    return function (div, vm) {
        keepvm = vm;
        clearTimeout(timer);
        timer = setTimeout(build(div), 400);
    };
}
let readMessageMetioned = buildMessageMetioned();

let containers, containerDom;
const CHAT_TYPE = {
    // MESSAGE: 'MessageTxtChat', PictureChat NameCard
    VCARD: 'NameCard',
    VOICE: 'VoiceChat',
    MESSAGE: 'MessageTxtRichChat',
    MESSAGELONG: 'MessageTxtRichChat',
    UNKNOW: 'UnknowChat',
    UNSUPPORTED: 'UnsupportedChat',
    RESTRICTED: 'MessageTxtChat',
    E2ETIPS: 'E2ETips',
    E2EKeyChangeTip: 'E2EKeyChangeTip',
    TIME: 'TimeChat',
    EVENT: 'EventChat',
    PICTURE: 'PictureChat',
    STICKER: 'StickerChat',
    GIF: 'GifChat',
    DOCUMENT: 'DocumentChat',
    RICHURL: 'RichUrlChat',
    MEETINGINVITE: 'MeetingInviteChat',
    Announcement: 'AnnouncementChat',
    CombineText: 'CombineChat',
    RobotText: 'MessageTxtChat',
    RobotRich: 'RobotRichChat',
    ForRobotRich: 'ForRobotRichChat',
    CALLRECORD: 'CallRecordChat',
    E2EDESCRIPT: 'E2EDescript',
    MSGCARD: 'MessageCard',
    MEETINGCARD: 'MeetingCard',
    APPROVALCARD: 'ApprovalCard',
    APPROVALFORWARDCARD: 'ApprovalForwardCard',
    MAPVIEW: 'MapView',
    RICHTEXT: 'MessageComponents'
};
const LIMIT_IMG_TYPE = ['image/jpeg', 'image/webp', 'image/jpg', 'image/png', 'image/bmp', 'image/gif', 'application/sticker'];

export default {
    name: 'RightContentPanel',
    mixins: [rightOptionsMix, chatTranslateMix],
    directives: {
        timeChange: timeChange
    },
    components: {
        ReplyListPannel,
        GroupPinPannel,
        // eslint-disable-next-line vue/no-unused-components
        E2ETips,
        // eslint-disable-next-line vue/no-unused-components
        E2EKeyChangeTip,
        // eslint-disable-next-line vue/no-unused-components
        E2EDescript,
        E2EVerifyCode,
        TimeTip,
        ContactCard,
        RecipientCard,
        // BiDialog,
        // eslint-disable-next-line vue/no-unused-components
        NameCard,
        // eslint-disable-next-line vue/no-unused-components
        VoiceChat,
        // eslint-disable-next-line vue/no-unused-components
        PreSendFile,
        // eslint-disable-next-line vue/no-unused-components
        Nickname,
        // eslint-disable-next-line vue/no-unused-components
        messageRecept,
        // eslint-disable-next-line vue/no-unused-components
        MessageTxtChat,
        // eslint-disable-next-line vue/no-unused-components
        AnnouncementChat,
        // eslint-disable-next-line vue/no-unused-components
        CombineChat,
        // eslint-disable-next-line vue/no-unused-components
        RobotRichChat,
        // eslint-disable-next-line vue/no-unused-components
        ForRobotRichChat,
        // eslint-disable-next-line vue/no-unused-components
        MessageTxtRichChat,
        // eslint-disable-next-line vue/no-unused-components
        EventChat,
        // eslint-disable-next-line vue/no-unused-components
        PictureChat,
        MessageInput,
        // eslint-disable-next-line vue/no-unused-components
        ViewScroll,
        // eslint-disable-next-line vue/no-unused-components
        FloatTip,
        ChatHead,
        // eslint-disable-next-line vue/no-unused-components
        TimeChat,
        // eslint-disable-next-line vue/no-unused-components
        ViewDialog,
        // eslint-disable-next-line vue/no-unused-components
        Portrait,
        // Icon,
        // eslint-disable-next-line vue/no-unused-components
        DocumentChat,
        RightClickOption,
        SendFileDialog,
        ReplyPannel,
        // eslint-disable-next-line vue/no-unused-components
        MapView,
        // eslint-disable-next-line vue/no-unused-components
        MeetingInviteChat,
        // eslint-disable-next-line vue/no-unused-components
        CallRecordChat,
        // eslint-disable-next-line vue/no-unused-components
        PanelSplit,
        // eslint-disable-next-line vue/no-unused-components
        MessageCard,
        // eslint-disable-next-line vue/no-unused-components
        MeetingCard,
        // eslint-disable-next-line vue/no-unused-components
        ApprovalCard,
        // eslint-disable-next-line vue/no-unused-components
        ApprovalForwardCard,
        // eslint-disable-next-line vue/no-unused-components
        MessageComponents,
        // eslint-disable-next-line vue/no-unused-components
        UnknowChat,
        // eslint-disable-next-line vue/no-unused-components
        UnsupportedChat,
        Backtarget,
        ConcatPopover,
        FeedbackDialog,
        RightMessageEdit
    },
    provide() {
        return {
            setViewChatList: this.setViewChatList,
            getViewChatList: this.getViewChatList,
            isSupportedForwards: this.isSupportedForwards,
            setLimitUserSelectCheckbox: this.setLimitUserSelectCheckbox,
            clickRecipient: this.clickRecipient,
            clickContactInfo: this.clickContactInfo,
            doubleContactCard: this.doubleContactCard,
            commentHandler: this.commentHandler
        };
    },
    data() {
        return {
            isDebug,
            recipientCardObj: {
                showValue: false
            },
            GroupInfoMemberObjs: {},
            isHideMainWindow: false,
            visibleHidePopover: false,
            prevTipTime: 0,
            pinDialog: {},
            pinDialogVisable: true,
            showEmoji: false,
            dataInited: false,
            showMetion: true,
            showed: false,
            visibleMetion: false,
            isE2EFaile: false,
            isExistCurrentSpace: true,
            newMessagesUUID: null,
            newMessagesNumer: 0,
            visibleArrowUp: true,
            //浮动的时间
            recentTime: '',
            contactCard: {
                visible: false,
                who: 0,
                peer: {},
                contentStyle: {}
            },
            willSendMsg: '',
            visible: true,
            lastChatItem: '',
            isFromCurrentTokenChange: false,
            lastMsgTimestampMap: {},
            mineInfo: '',
            isFetchMessage: false,
            messageListHeight: '560px',
            // fileIcon,
            // fileIconActive,
            // emojiIcon,
            // emojiIconActive,
            // sendIconActive,
            groupMembers: [],
            isFocus: false,
            optionActIdx: '',
            dbList: [],
            pageSize: 20,
            curPage: 1,
            // @消息下标
            remindIndex: -1,
            isRemind: false,
            // 是否是当前回话@消息 uuid
            isCurUuid: false,
            tmpCount: 0,
            dialogTableVisible: false,
            readlist: [],
            notreadlist: [],
            // 灰色背景下标
            positionIndex: -1,
            // 点击会话和点击标签 禁止加载 顶部和底部触发方法
            isPosition: false,
            // 滚动底部页码
            curPageBottom: 0,
            // 点击标签定位区间加载 loding
            clickLoading: false,
            // 当前 @消息uuid
            curUuid: '',
            isShowDown: false,
            groupInfo: {},
            viewChatList: [],
            // 当前view是否是包含最新消息的view
            isNewestView: true,
            // 右键选择框偏移值
            tranLeft: 0,
            tranTop: 0,
            tranRight: 'auto',
            // 右键菜单选项
            rightPanelOptions: [],
            // 激活的消息对象
            currentChatInfo: {},
            //当前窗口的reply的消息对象
            currentDialogReplyInfo: {
                // reply回复框开关
                replyPannelVisible: false,
                replyChatInfo: {}
            },
            // 转发窗口
            // openedMembersToBeForward: false,
            // typeNameMembersToBeForward: '',
            // 名片窗口
            // namecardDialogVisable: false,
            hoverFileIcon: false,
            notOrientation: false,
            shortcutstr: '',
            queueManager: {push: () => Promise.resolve}, // 转发消息管理
            readCaches: [],
            readCachesArr: [],
            receiptTopBtnName: 'no',
            scrollBottomNewId: '',
            isExistFromDBMemberInCurrentSpace: false,
            fetchMessageKey: '',
            scrollNewMessageId: '',
            currentInputHeight: 170,
            editMessageState: {
                uuid: ''
            }
        };
    },
    computed: {
        ...mapState('userInfo', {
            hid: 'hid',
            meetingStatus: 'meetingStatus'
        }),
        ...mapState('emojiReply', ['visibleEmojiReply']),
        ...mapState('uiControl', [
            'isEmojiPannelVisable',
            'suggestionPannelVisable',
            'metionedList',
            'actDialogId',
            'downloadFileDialogVisible',
            'webContentsFocus',
            'isMultipleSelected'
        ]),
        ...mapState('spaceCollection', ['currentSpaceId']),
        ...mapState('concat', ['contactsInitDone']),
        ...mapGetters('spaceLimit', [
            'fileAcceptInfo',
            'fileAcceptValue',
            'imageLimitInfo',
            'isCapture',
            'isSendCard',
            'isUseMessageTranslation',
            'isSaveMultiMedia'
        ]),
        ...mapGetters('setting', ['receiptsSwitch']),
        ...mapGetters('setting', ['receiptsSwitch']),
        ...mapGetters('concat', ['isExistMemberInCurrentSpace']),
        ...mapGetters('fileCollection', ['getFileObj']),
        ...mapGetters('spaceCollection', ['getCurrentSpaceType', 'isWinFocus', 'getSpaceInfo']),
        ...mapGetters('uiControl', ['getPeerInfo', 'actDialogIdIsGroup', 'chatContextMenuVisible']),

        actDialogUid() {
            return this.actDialogId ? dataUtil.hidToNumber(this.actDialogId) : '';
        },

        isGroup() {
            return this.peerType === 'group';
        },
        isUser() {
            return this.peerType === 'user';
        },
        viewChatCheckedList() {
            const viewChatList = this.viewChatList;
            return this.getViewChatCheckedList(viewChatList);
        },
        isSubscribe() {
            const isSubscribe = this.peerType === 'subscribe';
            return isSubscribe;
        },
        peerType() {
            return dataUtil.getPeerType(this.currentReadToken);
        },
        backTargetOptions() {
            return {
                scrollToMetioned: this.scrollToMetioned,
                scrollNewMessage: this.scrollNewMessage,
                scrollBottomNew: this.scrollBottomNew,
                metionedList: this.metionedList,
                showMetion: this.showMetion,
                visibleArrowUp: this.visibleArrowUp,
                newMessagesNumer: this.newMessagesNumer,
                candown: this.candown,
                badgeCount: this.badgeCount
            };
        },
        mStatus() {
            return this.$store.state.messageCollection.mStatus;
        },
        viewChatListComputed() {
            const mStatus = this.mStatus;
            let currentViewList = this.viewChatList.map(item => {
                // 消息发送失败
                const isFail = F.isFailLogic(mStatus, item);
                // 消息发送中
                const isSending = F.isSendingLogic(mStatus, item);
                if (Boolean(item.isFail) !== isFail || Boolean(item.isSending) !== isSending) {
                    item.isFail = isFail;
                    item.isSending = isSending;
                    // 解决isFail OR isSending更新，但是视图没有更新问题
                    return Object.assign({}, item, {isFail, isSending});
                    // return JSON.parse(JSON.stringify(item));
                }
                return item;
            });

            currentViewList = currentViewList.filter(item => item.plainMsg?.dialogId === this.actDialogId && item.plainMsg?.burned !== 1);

            if (this.$store.state.uiControl.actDialogId === this.$store.state.userInfo.hid) {
                let UserSelfMsg = renderUserSelfTemplate();
                if (currentViewList.length > 0) {
                    UserSelfMsg.isShowDate = true;
                    UserSelfMsg.plainMsg.stime = currentViewList[0].plainMsg.stime;
                    currentViewList[0].hideDate = true;
                    currentViewList = [UserSelfMsg].concat(currentViewList);
                }
            }

            const peerType = dataUtil.getPeerType(this.$store.state.uiControl.actDialogId);
            if (this.showed && peerType != 'group' && peerType != 'subscribe' && defaultSpaceId() == 'UAE-971-1000000') {
                const e2eMsg = renderE2ETemplate();
                if (currentViewList.length > 0 && this.getCurrentSpaceType) {
                    return [e2eMsg].concat(currentViewList);
                } else {
                    return currentViewList;
                }
            }
            return currentViewList;
        },
        candown() {
            return this.$store.state.uiControl.currentDialogIsBottomed == false;
        },
        hideDialogUnRead() {
            return this.$store.state.uiControl.hideDialogUnRead == false;
        },
        badgeCount() {
            return this.$store.state.sessionCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())]
                ? this.$store.state.sessionCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())].unreadCount
                : 0;
        },
        sendDialogDisplay: {
            get() {
                // console.log('rightContent get');
                return this.$store.state.uiControl.sendDialogVisble;
            },
            set(newV) {
                // console.log('rightContent set', newV);

                this.$store.state.uiControl.sendDialogVisble = newV;
                this.$nextTick(() => {
                    // console.log('xxxxx dialogMessageInputer');
                    this.$refs.sendFileRef.$refs.dialogMessageInputer.$refs.contentEditor.focus();
                });
            }
        },
        dialogUserInfo(current) {
            return {
                displayName: current.displayName || current.name,
                token: current.hid
            };
        },
        currentReadToken() {
            return this.$store.getters['uiControl/getActDialogId'];
        },
        // groupstatus() {
        //   if (
        //     this.$store.state.uiControl.actDialogId &&
        //     this.$store.state.peerCollection[
        //       enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //     ].groupInfo
        //   ) {
        //     return this.$store.state.peerCollection[
        //       enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //     ].bannedSwitch;
        //   }
        // },
        // 当前会话的peerInfo
        currentDialogPeerInfo() {
            if (
                this.$store.state.uiControl.actDialogId &&
                this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())]
            ) {
                return this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())];
            } else {
                return {};
            }
        },
        // userstatus() {
        //   if (
        //     this.$store.state.uiControl.actDialogId &&
        //     this.$store.state.peerCollection[
        //       enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //     ]
        //   ) {
        //     return this.$store.state.peerCollection[
        //       enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //     ].userstatus;
        //   }
        //   return 0;
        // },
        //  myselfstatus() {
        //   if (
        //     this.$store.state.uiControl.actDialogId &&
        //     this.$store.state.peerCollection[
        //       enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //     ]?.groupInfo
        //   ) {
        //     return this.$store.state.peerCollection[
        //       enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //     ].bannedSwitch
        //       ? this.$store.state.peerCollection[
        //           enCodeSpaceHid(this.$store.state.uiControl.actDialogId,defaultSpaceId())
        //         ].bannedMembers
        //       : [];
        //   }
        //   return [];
        // },
        isManager() {
            let selfToken = this.$store.state.userInfo.hid;
            let Groupinfo = this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())];
            if (Groupinfo.owner == selfToken) {
                return true;
            }
            return false;
        },
        // 群组成员列表
        grouplist() {
            if (
                this.$store.state.uiControl.actDialogId &&
                this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())]?.groupInfo
            ) {
                return (
                    this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())].groupInfo
                        .grouplist || []
                );
            }
            return [];
        },
        // 是否为自己发消息
        _isMine() {
            let newMessageObj = this.viewChatList[this.viewChatList.length - 1] || {};
            return newMessageObj.plainMsg && newMessageObj.plainMsg.isMine;
        },
        isShowFloatTip() {
            return {
                isShowUp: this.isShowUp,
                isShowDown: this.isShowDown,
                isRemind: this.isRemind
            };
        },
        floatTipType() {
            return this.isRemind ? 'AT' : 'NEW';
        },
        // 当前会话 计数
        downUnReadCount() {
            return this.$store.state.uiControl.CurDialogAcount;
        },
        upUnreadCount() {
            return this.$store.state.uiControl.unReadCount;
        },
        isShowUp() {
            return this.upUnreadCount > this.pageSize ? true : false;
        },

        mySession() {
            let peer = this.$store.state.sessionCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())];
            return peer;
        },
        // 自己拉黑对方
        iWasBlocked() {
            return this.currentDialogPeerInfo?.myBlock ? true : false;
        },
        // 我是否被对方block
        isBeBlockInE2EE() {
            return !!(this.getCurrentSpaceType && this.currentDialogPeerInfo && this.currentDialogPeerInfo.isBeBlock);
        },
        isFriend() {
            return this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())].type ==
                'friend'
                ? true
                : false;
        },
        isBlacklist() {
            // 自己拉黑对方
            if (this.isBeBlockInE2EE) {
                return ConstantTypes.BLOCK_STATUS_IWAS;
            }
            // 被对方拉黑
            if (this.iWasBlocked) {
                return ConstantTypes.BLOCK_STATUS_BE;
            }
            // 其他
            return ConstantTypes.BLOCK_STATUS_NORMAL;
        },
        firstSeqMsg() {
            return this.viewChatList.find(item => item.msgSeq);
        },
        endSeqMsg() {
            return this.viewChatList.findLast(item => item.msgSeq);
        },
        firstSeq() {
            return this.firstSeqMsg?.msgSeq;
        },
        lastSeq() {
            return this.endSeqMsg?.msgSeq;
        }
    },
    watch: {
        isBlacklist: {
            immediate: true,
            handler(value) {
                this.setBlacklist(value);
            }
        },
        metionedList(val, old) {
            if (old.length != 0) {
                void null;
            } else {
                this.showMetion = false;
                setTimeout(() => {
                    this.showMetion = true;
                }, 1000);
            }
        },
        currentReadToken: {
            deep: true,
            immediate: true,
            handler: async function (val, oldVal) {
                this.fetchMessageKey = uuidv4();
                const fetchMessageKey = this.fetchMessageKey;
                // 停止播放
                Bus.$emit('voice-play', '');
                Bus.$off('startPullDetail');
                msgDevLog.log('currentReadToken watch:', val);

                // if (oldVal != null) {
                // console.log('clear last info of AT');
                // clearMentioned(oldVal).then(() => {
                // });
                // this.$refs.messageInputer?.$refs?.contentEditor &&
                //     draft(oldVal + defaultSpaceId(), this.$refs.messageInputer.$refs.contentEditor.innerHTML);
                // }
                // 强制刷新左边会话列表短消息
                let key = 'newmsg__' + val;
                if (window[key]) window[key]();
                clearTimeout(this.E2EEScanTimer);
                this.$refs.messageInputer && this.$refs.messageInputer.setDraft(''); // 切换会话 draft更新慢的问题
                this.dataInited = false;
                this.showed = false;
                this.visibleMetion = false;
                statusOfLoading = false;
                this.viewChatList = [];
                window.historyMessage = {};
                window.historyMessageIs = {};

                window.fetchRegion = {};

                this.clearMsgQueue();

                this.newMessagesNumer = 0;
                this.visibleArrowUp = false;
                this.isE2EFaile = false;
                this.showMetion = false;
                this.isNewestView = true;
                this.recentTime = '';
                this.editMessageState.uuid = '';
                this.editMessageState.stime = '';

                // console.log('所有操作之前');
                try {
                    this.newMessagesNumer = this.mySession?.unreadCount || 0;
                    // 获取群信息
                    this.recipientCardObj.members = [];
                    this.getExistFromDBMemberInCurrentSpace();

                    this.getActiveReplyData();
                    if (defaultSpaceId() == personalSpaceId()) {
                        await this.checkE2EEDeviceMap();
                    }
                    await this.takeFirstUUIDUnread(this.newMessagesNumer);
                    this.viewChatList = await this.fetchMessage({forceStime: 0});

                    this.getServerMaxSeq();
                } catch (e) {
                    console.error('[error]: ', 'currentReadToken err', e, this.$store.state.uiControl.actDialogId, defaultSpaceId());
                    msgDevLog.error('[error]: ', 'currentReadToken err', e, this.$store.state.uiControl.actDialogId, defaultSpaceId());
                }

                if (this.isGroup) {
                    Bus.$emit('reset-group-pin-unread');
                }

                if (this.getSpaceInfo(this.currentSpaceId)?.destroyState === 1 && this.isUser) {
                    this.refreshFriendDescribe(val, this.currentSpaceId);
                }

                if (this.isUser && val !== this.hid) {
                    subscribePeerStatus(val);
                } else {
                    unsubscribePeerStatus();
                }

                this.showed = true;
                this.$nextTick(() => {
                    this.setLimitUserSelectCheckbox('a3');
                    this.initCurrentMessageList(fetchMessageKey);

                    // 渲染完毕更新草稿
                    let oldObj =
                        this.$store.state.sessionCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())];
                    this.$refs.messageInputer && this.$refs.messageInputer.setDraft(oldObj?.draftObj?.value || '');
                    // console.log('draft== messageInputer', val + defaultSpaceId());

                    this.autoVisible();
                    this.dataInited = true;
                    this.ifLoadMoreIsNeeded(containerDom, this);
                    keepLastIndex(this.$refs.messageInputer?.$refs.contentEditor);

                    // 主动垃圾回收
                    window.clearCacheMemory();

                    this.showMetion = true;
                });
            }
        }
    },
    methods: {
        ...mapMutations('emojiReply', ['setBlacklist', 'setVisibleEmojiReply']),
        ...mapMutations('fileCollection', ['clearFileList']),
        ...mapActions('uiControl', ['switchOne', 'setCurrentDialogBottom', 'setChatContextMenu']),
        ...mapMutations('uiControl', ['setPullLoading']),
        ...mapActions('uiControl', ['openDialogCheckList']),
        initCurrentMessageList(key) {
            if (key === this.fetchMessageKey) {
                // console.log('viewChatList', this.viewChatList, this.viewChatList.length);
                let el = F.paths(this, '$refs', 'container_view', '$el');

                // console.log('scrollHeight', el, el.scrollHeight, el.offsetHeight);
                if (this.viewChatList.length <= 2 || (el && el.scrollHeight <= el.offsetHeight)) {
                    let renderMsg = this.viewChatList[0];
                    if (!(renderMsg?.customData?.msg === 'You cleared all past messages')) {
                        this.setPullLoading(true);

                        if (renderMsg) {
                            if (appdataStorage.getItem('c_socket_state') === 'WPushRes' || appdataStorage.getItem('imSdkOpen')) {
                                msgDevLog.log('startPullDetail00', renderMsg?.uuid);
                                this.startPullDetail(renderMsg);
                                this.pullLoadingTimer = window.setTimeout(() => {
                                    statusOfLoading = false;
                                    window.clearTimeout(this.pullLoadingTimer);
                                    this.setPullLoading(false);
                                }, 5000);
                            } else {
                                msgDevLog.warn('startPullDetail', renderMsg);
                                Bus.$on('startPullDetail', () => {
                                    msgDevLog.log('startPullDetail11', renderMsg);
                                    this.startPullDetail(renderMsg);
                                    Bus.$off('startPullDetail');
                                    window.clearTimeout(this.pullLoadingTimer);
                                    this.pullLoadingTimer = window.setTimeout(() => {
                                        statusOfLoading = false;
                                        window.clearTimeout(this.pullLoadingTimer);
                                        this.setPullLoading(false);
                                    }, 5000);
                                });
                                this.pullLoadingTimer = window.setTimeout(() => {
                                    statusOfLoading = false;
                                    window.clearTimeout(this.pullLoadingTimer);
                                    this.setPullLoading(false);
                                }, 8000);
                            }
                        } else {
                            this.sendPullNewDetail();
                            this.pullLoadingTimer = window.setTimeout(() => {
                                statusOfLoading = false;
                                window.clearTimeout(this.pullLoadingTimer);
                                this.setPullLoading(false);
                            }, 5000);
                        }
                    }
                }
                this.$nextTick(() => {
                    this.$store.commit('uiControl/currentDialogBottom', true);
                    let bottomdiv = document.getElementById('bottomdiv');

                    if (this.viewChatList.length < 14) {
                        // handle have read and unread when there isn't scrollbar
                        readMessageMetioned(el, this);
                        // this.$refs.container_view?.$el.scrollTo(0, 1000000);
                    } else {
                        // this.$refs.container_view?.$el.scrollTo(0, 1000000);
                    }
                    // console.log('bottomdiv', bottomdiv);
                    if (bottomdiv) {
                        bottomdiv.scrollIntoView(true);
                    }

                    this.renderTimeTip();
                });
            } else {
                console.warn('initCurrentMessageList', this.currentReadToken);
                msgDevLog.warn('initCurrentMessageList', this.currentReadToken);
            }
        },
        startPullDetail(renderMsg) {
            window.historyMessage[renderMsg.uuid] = [];
            window.historyMessageIs[renderMsg.uuid] = {};
            window.historyMessage['function' + renderMsg.uuid] = () => {
                // console.log('-----VVVVVVVVV-------')
                statusOfLoading = false;
                // console.log('什么情况', 'function' + renderMsg.uuid);
                this.scrollTopLoadMore_('again', 'init');
            };
            this.sendPullDetail(this.hid, renderMsg);
        },
        async getExistFromDBMemberInCurrentSpace() {
            if (!this.isExistMemberInCurrentSpace) {
                // let hostId = await getHid();
                let hostId = this.hid;
                let typeOfPeer = await getWith(
                    'select type from peer where isDeleted is null and hid=:dialogId and hostId=:hostId',
                    {
                        dialogId: this.actDialogId,
                        hostId
                    },
                    defaultSpaceId()
                );
                if (typeOfPeer && (typeOfPeer.type == 'group' || typeOfPeer.type == 'friend')) {
                    this.isExistFromDBMemberInCurrentSpace = true;
                } else if (defaultSpaceId() != 'UAE-971-1000000' || dataUtil.getIsWhiteList(this.currentReadToken)) {
                    this.isExistFromDBMemberInCurrentSpace = true;
                } else {
                    this.isExistFromDBMemberInCurrentSpace = false;
                }
            } else {
                this.isExistFromDBMemberInCurrentSpace = true;
            }
        },
        async watchFocusView() {
            try {
                // console.log('ipcSendGetPreceiptView', this.$refs.container_view);
                if (!this.$refs.container_view?.$el) return;
                this.renderTimeTip();
                await Peer.checkIfsendMsg(this.actDialogId);

                const scrollTop = this.$refs.container_view.$el.scrollTop;
                let viewScrollItem = document.getElementsByClassName('viewscroll-item');
                let viewFocusNum = '';
                let viewFocusList = [];
                let viewFocusIs = false;
                for (let i = 0; i < viewScrollItem.length; i++) {
                    const {offsetTop, id} = viewScrollItem[i];
                    if (offsetTop > scrollTop - 40) {
                        // console.log('object', offsetTop, innerText, id)
                        viewFocusNum = id;
                        break;
                    }
                }
                this.viewChatList.forEach(item => {
                    if (item.uuid === viewFocusNum) viewFocusIs = true;
                    if (viewFocusIs) viewFocusList.push(item);
                });
                console.log('ipcSendGetPreceiptView', viewFocusList);
                const SpaceId = defaultSpaceId();
                if (!viewFocusList.length) return;
                const actDialogId = this.actDialogId;
                const receiptsSwitch = this.receiptsSwitch;
                sendMsgReceiptInfo(viewFocusList, receiptsSwitch, actDialogId, SpaceId);
                // watchFocusViewReceipt(
                //     viewFocusList[0].uuid,
                //     viewFocusList[viewFocusList.length - 1].uuid,
                //     this.actDialogId,
                //     SpaceId,
                //     this.receiptsSwitch
                // );
            } catch (error) {
                console.error('[error]: ', 'watchFocusView', error);
            }
        },
        setReadedUuid({uuid}) {
            let viewItem = this.viewChatList.find(item => item.uuid === uuid);

            viewItem && this.readCaches.push(viewItem);
            const actDialogId = this.actDialogId;
            const readCaches = this.readCaches;
            // console.log('IntersectionObserver', uuid, actDialogId);
            this.debouncedReadedUuids(readCaches, actDialogId);
        },
        async changeReadedUuids(viewChatList, actDialogId) {
            try {
                // const actDialogId = this.actDialogId;
                // console.log('IntersectionObserver changeReadedUuids', actDialogId, this.receiptTopBtnName);
                await Peer.checkIfsendMsg(actDialogId);
                const SpaceId = defaultSpaceId();
                const newHidId = viewChatList[0]?.uuid;
                const oldHidId = this.readCachesArr[0]?.uuid;
                switch (this.receiptTopBtnName) {
                    case 'no':
                        if (this.webContentsFocus) {
                            sendMsgReceiptInfo(viewChatList, this.receiptsSwitch, actDialogId, SpaceId);
                        }
                        break;
                    case 'scrollToMetioned':
                        // console.log('IntersectionObserver scrollToMetioned', newHidId, oldHidId, actDialogId, SpaceId);
                        scrollToMetionedReceipt(this.scrollToMetionedId, oldHidId, actDialogId, SpaceId, this.receiptsSwitch);
                        break;
                    case 'scrollNewMessage':
                        console.log(
                            'IntersectionObserver scrollNewMessage',
                            this.scrollNewMessageId,
                            oldHidId,
                            actDialogId,
                            SpaceId,
                            this.receiptsSwitch
                        );
                        scrollNewMessageReceipt(this.scrollNewMessageId, oldHidId, actDialogId, SpaceId, this.receiptsSwitch);
                        break;
                    case 'scrollBottomNew':
                        // console.log('IntersectionObserver scrollBottomNew', newHidId, oldHidId, actDialogId, SpaceId, this.receiptsSwitch);
                        scrollBottomNewReceipt(this.scrollBottomNewId, newHidId, actDialogId, SpaceId, this.receiptsSwitch);
                        // sendMsgReceiptInfo(viewChatList, this.receiptsSwitch, actDialogId, SpaceId);
                        break;
                }
                this.readCachesArr = viewChatList;
                this.receiptTopBtnName = 'no';
                this.readCaches = [];
            } catch (e) {
                console.error('[error]: ', 'changeReadedUuids=>', e);
            }
        },
        getHideMainWindow,
        setHideMainWindow,
        onSelectShareClose() {
            this.$store.commit('uiControl/setMultipleSelected', false);
            this.$forceUpdate();
        },
        onSelectShareButton() {
            if (this.viewChatCheckedList.length === 0) {
                this.$message(this.$t('selectedPannel.pleaseChooseOne'));
                return;
            }
            this.typeNameMembersToBeForward = 'selectShare';
            // this.openedMembersToBeForward = true;
            this.openDialogCheckList('forwardMessage');
        },
        onSelectCombineButton() {
            if (this.viewChatCheckedList.length === 0) {
                this.$message(this.$t('selectedPannel.pleaseChooseOne'));
                return;
            }
            this.typeNameMembersToBeForward = 'selectShare';
            // this.openedMembersToBeForward = true;
            this.openDialogCheckList('forwardCombineMessage');
        },
        async onBatchSaveSelectFile() {
            /**
             * File download check sequence
             * 1. configDir should be greater than downloadMinDiskSize 50M
             * 2. CHOOSE-PATH should be greater than the total size of the selected files
             * 3. checkPathPermitted CHOOSE-PATH
             * 4. If download is needed, currentDownloadPath should be greater than downloadDiskSize 2G
             * 5. If download is needed, checkPathPermitted currentDownloadPath
             */

            F.checkCurrentSpaceExpire();

            if (this.viewChatCheckedList.length === 0) {
                this.$message(this.$t('selectedPannel.pleaseChooseOne'));
                return;
            }

            const saveMessageList = this.viewChatCheckedList.filter(item => {
                if (item?.chatComponentType === 'PICTURE' || item?.chatComponentType === 'DOCUMENT') {
                    return true;
                }
                if (item?.chatComponentType === 'RICHTEXT') {
                    return item?.plainMsg?.m?.meta?.medias?.some(m => m?.type === 'image');
                }
                return false;
            });

            if (!saveMessageList?.length) {
                this.$message(this.$t('selectedPannel.noFile'));
                return;
            }

            const diskSpace = await checkUserDiskFreeSpace(this.$store.state.config.configDir);

            if (diskSpace && !diskSpace.error && diskSpace.free <= downloadMinDiskSize) {
                console.error('[error]: ', 'onBatchSaveSelectFile', diskSpace);
                devFileLog.error('onBatchSaveSelectFile error', diskSpace);

                this.$message({
                    type: 'warning',
                    message: this.$t('errorCode.error_space_noleft'),
                    duration: 5000
                });
                throw {
                    status: 'warning',
                    message: this.$t('errorCode.error_space_noleft')
                };
            }

            if (saveMessageList.length !== this.viewChatCheckedList.length) {
                await this.$confirm(this.$t('selectedPannel.onlySaveFile'), this.$t('selectedPannel.Notes'), {
                    confirmButtonText: this.$t('base.ok'),
                    cancelButtonText: this.$t('canel'),
                    confirmButtonClass: 'm-button full-button full-button--blue',
                    customClass: 'm-message-box',
                    showClose: false
                });
            }

            try {
                const filePath = await ipcRenderer.invoke('CHOOSE-DIRECTORY-PATH', this.$store.state.uiControl.currentDownloadPath);
                console.log(`🚀 ~ onBatchSaveSelectFile ~ filePath:`, filePath);

                if (filePath && filePath[0]) {
                    const downloadPath = filePath[0];

                    let fileTotalSize = saveMessageList.reduce((accumulator, current) => {
                        if (current?.chatComponentType === 'RICHTEXT') {
                            const total = current?.plainMsg?.m?.meta?.medias?.reduce((richAccumulator, medias) => {
                                return richAccumulator + medias?.download?.size || 0;
                            }, 0);

                            console.log(`🚀 ~ total ~ total:`, total);

                            return accumulator + total;
                        }

                        return accumulator + current?.plainMsg?.m?.meta?.download?.size || 0;
                    }, 0);

                    const diskSpace = await checkUserDiskFreeSpace(downloadPath);
                    console.log(`🚀 ~ onBatchSaveSelectFile ~ diskSpace:`, fileTotalSize, downloadPath, diskSpace);

                    if (diskSpace && !diskSpace.error && diskSpace.free <= fileTotalSize) {
                        this.$confirm(
                            this.$t('selectedPannel.NoEnoughStorageSpaceContent'),
                            this.$t('selectedPannel.NoEnoughStorageSpace'),
                            {
                                confirmButtonText: this.$t('base.ok'),
                                showCancelButton: false,
                                confirmButtonClass: 'm-button full-button full-button--blue',
                                customClass: 'm-message-box',
                                showClose: false
                            }
                        );

                        devFileLog.error('onBatchSaveSelectFile downloadPath checkUserDiskFreeSpace', fileTotalSize, diskSpace);
                        return;
                    }

                    const check = await checkPathPermitted(downloadPath);
                    if (!check) {
                        this.$message(this.$t('right_content_panel.download-file.diskNoWritable'));
                        devFileLog.error('onBatchSaveSelectFile downloadPath checkPathPermitted false');
                        return;
                    }

                    const isNeedDownload = saveMessageList.some(
                        item => !item.plainMsg?.assertPath || !Tool.existFile(item.plainMsg.assertPath)
                    );

                    if (isNeedDownload) {
                        const diskSpace = await checkUserDiskFreeSpace(this.$store.state.uiControl.currentDownloadPath);
                        if (diskSpace && !diskSpace.error && diskSpace.free <= downloadDiskSize) {
                            devFileLog.error('onBatchSaveSelectFile currentDownloadPath error', diskSpace);

                            this.$message(this.$t('errorCode.error_space_noleft'));
                            return;
                        }

                        const check = await checkPathPermitted(this.$store.state.uiControl.currentDownloadPath);
                        if (!check) {
                            this.$message(this.$t('right_content_panel.download-file.diskNoWritable'));

                            devFileLog.error('onBatchSaveSelectFile currentDownloadPath checkPathPermitted false');
                            return;
                        }

                        this.$message(this.$t('selectedPannel.Saving'));
                    }
                    BatchFileSaver.saveFileList(_.cloneDeep(saveMessageList), downloadPath);
                    devFileLog.log('onBatchSaveSelectFile saveFileList', downloadPath);
                    this.onSelectShareClose();
                }
            } catch (error) {
                devFileLog.error('onBatchSaveSelectFile checkPathPermitted false', error?.message);
            }
        },
        // 设置
        setViewChatList(viewChatList) {
            this.viewChatList = viewChatList;
        },
        getViewChatList() {
            return this.viewChatList;
        },
        getViewChatCheckedList(viewChatList = []) {
            return viewChatList.filter(item => item.isMultipleSelectChecked);
        },
        // Set disabled status
        setLimitUserSelectCheckbox(mark) {
            if (!this.isMultipleSelected) {
                return;
            }
            const limitCount = 50; // 限制选中个数

            // isNotSupportForward 默认禁用(消息不支持转发)
            // isMultipleSelectDisabled 超出规则禁用(超出选中条件的禁用)
            const viewChatCheckedList = this.getViewChatCheckedList(this.getViewChatList() || []);
            let viewChatList; // 数据源
            if (viewChatCheckedList.length >= limitCount) {
                // 超出转发的条数 未选中的checkbox将被禁用
                viewChatList = (this.getViewChatList() || []).map(item => {
                    // "C9E07B44-E137-4912-BA94-C55E88FE325C"
                    if (item.isMultipleSelectChecked) {
                        return item;
                    } else {
                        return {...item, isMultipleSelectDisabled: true};
                    }
                });
            } else {
                // 未超出转发的条数 将被取消checkbox禁用
                viewChatList = (this.getViewChatList() || []).map(item => {
                    if (!item.isMultipleSelectDisabled) {
                        return item;
                    }
                    return {...item, isMultipleSelectDisabled: false};
                });
            }

            this.$nextTick(() => {
                this.setViewChatList(viewChatList);
            });
        },
        detectVisibleItems() {
            const viewChatList = this.getViewChatList() || [];
            console.log('visible-items-change', viewChatList);

            const container = this.$refs.container_view?.$el;
            const containerRect = container.getBoundingClientRect();
            const containerTop = container.scrollTop;
            const containerHeight = container.clientHeight;

            const visibleItems = [];

            viewChatList.forEach(item => {
                const element = document.getElementById(item.uuid);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    const elementTop = rect.top - containerRect.top + containerTop;
                    const elementBottom = elementTop + rect.height;

                    // 判断是否在可视区域内
                    if (
                        (elementTop >= containerTop && elementTop <= containerTop + containerHeight) ||
                        (elementBottom >= containerTop && elementBottom <= containerTop + containerHeight) ||
                        (elementTop <= containerTop && elementBottom >= containerTop + containerHeight)
                    ) {
                        visibleItems.push(item);
                    }
                }
            });

            if (visibleItems.length > 0) {
                console.log('visible-items-change', visibleItems);
                return visibleItems;
            } else {
                return [];
            }
        },
        selectFollowingMessage() {
            const limitCount = 50;
            // this.detectVisibleItems();
            const viewChatList = this.getViewChatList() || [];
            const viewChatListCheck = this.detectVisibleItems() || [];
            const viewChatCheckedList = viewChatList.filter(item => item.isMultipleSelectChecked);

            console.log('viewChatCheckedList', viewChatCheckedList);
            const remaining = limitCount - viewChatCheckedList.length;
            if (remaining <= 0) {
                this.$message({type: 'info', message: this.$t('selectedPannel.Limit30Data', {limit: 50})});
                return;
            }

            const visibleUncheckedMessages = viewChatListCheck
                .filter(item => !item.isMultipleSelectChecked)
                .filter(item => !item.isNotSupportForward)
                .slice(viewChatList, remaining);

            const updatedList = viewChatList.map(item => {
                const shouldSelect = visibleUncheckedMessages.some(msg => msg.uuid === item.uuid);
                return shouldSelect ? {...item, isMultipleSelectChecked: true} : item;
            });

            this.setViewChatList(updatedList);
            this.setLimitUserSelectCheckbox();
        },
        addMentionHandle() {
            this.$refs.messageInputer.refEditorAddMention();
        },
        nameCardHandle() {
            Caught.errorWith(async () => {
                fireNameCardOpen();
                let current = this.$store.state.uiControl.actDialogId;
                await Peer.checkIfsendMsg(current);
                // this.namecardDialogVisable = true;
                // this.$nextTick(() => {
                //     this.$refs.nameCardDialogRef.resetData();
                //     this.$refs.nameCardDialogRef.resetSearchData(true);
                // });
                // Bus.$emit('reply-right-option-visable');
                this.openDialogCheckList('forwardNameCard');
                // this.openDialogCheckList('forwardMessage');
            });
        },
        onKeydownborad(type, event) {
            Bus.$emit('session-list-select-navigate', type);
            event.preventDefault();
        },
        scrollViewHandle(uuid, afterhook) {
            if (this.dataInited) {
                this.scrollToView(uuid, 0, afterhook);
            } else {
                setTimeout(() => {
                    this.scrollViewHandle(uuid, afterhook);
                }, 1000);
            }
        },
        closeGroupPannel() {
            this.$store.commit('uiControl/setGroupPinPannelVisible', false);
            this.$store.commit('uiControl/setReplyListPannelVisible', false);
            this.$refs.chatHeadRef.$refs.groupSettings.handleClose();
        },
        pageCaptureSendImg(event, url) {
            this.notOrientation = true;
            this.$refs.messageInputer.pasteHandle_({
                target: this.$refs.messageInputer.$refs.contentEditor,
                preventDefault: () => {},
                isNativeImage: true,
                base64Url: url
            });
        },
        onShowPopover() {
            const shortcutstr = getShortcut().join('+');
            this.shortcutstr = shortcutstr ? `(${shortcutstr})` : shortcutstr;
        },
        async groupPinAction() {
            try {
                fireMsgUnpined();
                await this.groupMessagePin(1, this.$store.state.uiControl.groupUnPinInfo);
                this.$store.state.uiControl.groupUnPinDialogVisible = false;
                // 刷新pin列表
                if (this.$store.state.uiControl.groupPinPannelVisible) {
                    Bus.$emit('reset-group-pin-list');
                }
            } catch (error) {
                console.error('[error]: ', 'groupPinAction', error);
            }
        },
        throttleScroll: _.throttle(function () {
            this.closeRightPanel();
        }, 300),
        produceOptions() {
            let res = this.RIGHT_CLICK_OPTIONS();
            return res;
        },
        produceReplyListRightOptions() {
            let res = this.REPLY_RIGHT_CLICK_OPTIONS();
            return res;
        },
        async groupMessagePinOption(info) {
            let currentOperation;
            if (
                (info.plainMsg && info.plainMsg.pinnedInfo && info.plainMsg.pinnedInfo.operation) == 1 ||
                info?.plainMsg?.m?.meta?.pinnedInfo?.pfrom
            ) {
                currentOperation = 1;
            } else {
                currentOperation = 0;
                fireMsgPined();
            }
            if (currentOperation == 1) {
                this.$store.state.uiControl.groupUnPinInfo = {
                    uuid: info.uuid,
                    stime: info.stime
                };
                this.$store.state.uiControl.groupUnPinDialogVisible = true;
            } else {
                await this.groupMessagePin(currentOperation, info, info.plainMsg);
            }
        },
        async producePinMsgConstruct({uuid, stime, currentOperation}) {
            let bePinMsg = await getMessageWithDatabase(uuid, defaultSpaceId());
            bePinMsg = bePinMsg.length > 0 ? bePinMsg[0] : '';

            if (bePinMsg) {
                bePinMsg.pinnedInfo = {
                    ptime: getTimestamp(),
                    pname: this.$store.state.userInfo.name,
                    uuid: uuid,
                    stime: stime,
                    operation: currentOperation ? 0 : 1,
                    pfrom: this.$store.state.userInfo.hid
                };
                if (currentOperation == 1) {
                    bePinMsg.pinnedInfo = null;
                    if (bePinMsg.content?.m?.meta && bePinMsg.content?.m?.meta?.pinnedInfo) {
                        bePinMsg.content.m.meta.pinnedInfo = null;
                    }
                    if (bePinMsg.m.meta && bePinMsg.m.meta.pinnedInfo) {
                        bePinMsg.m.meta.pinnedInfo = null;
                    }
                    if (bePinMsg.meta && bePinMsg.meta.pinnedInfo) {
                        bePinMsg.meta.pinnedInfo = null;
                    }
                }
                await this.$store.dispatch('messageCollection/saveMessage', {message: bePinMsg});
                if (window.newMsgComing) window.newMsgComing(bePinMsg);
            }
        },
        async groupMessagePin(currentOperation = 0, info, msg) {
            try {
                F.checkCurrentSpaceExpire();
                await Peer.checkIfsendMsg(this.$store.state.uiControl.actDialogId);
                await sendPinMsg({
                    eid: defaultSpaceId(),
                    fid: '+' + dataUtil.hidToNumber(this.$store.state.userInfo.hid),
                    tid: '+' + dataUtil.hidToNumber(this.$store.state.uiControl.actDialogId),
                    pinnedInfo: {
                        ptime: getTimestamp(),
                        pname: this.$store.state.userInfo.name,
                        uuid: info.uuid,
                        stime: info.stime,
                        operation: currentOperation ? 0 : 1
                    }
                });

                await this.producePinMsgConstruct({
                    uuid: info.uuid,
                    stime: info.stime,
                    currentOperation
                });

                if (currentOperation != 1) {
                    this.$message({
                        message: this.$t('right_content_panel.groupPin.PinnedItemAdded'),
                        type: 'success'
                    });
                }
            } catch (error) {
                if (error && error.responseHeader && error.responseHeader.status == '810') {
                    // this.$store.state.uiControl.groupPinTopToTenDialogVisible = true;
                    this.$message.error(this.$t('right_content_panel.group-pin-top-ten.tips1'));
                } else if (error && error.responseHeader && error.responseHeader.status == '601') {
                    this.$message.error(error.responseHeader.msg || this.$t('right_content_panel.groupPin.PinnedItemAdded'));
                } else {
                    if (error && error.msg) {
                        this.$message({
                            message: error.msg,
                            type: 'error'
                        });
                        return;
                    }
                    this.$message.error(this.$t('code500'));
                }
            }
        },
        downloadFileErrorHandle(uuid) {
            uuid && this.$refs[uuid]?.documentOperationHandle();
        },
        operateMessageHandle(data) {
            if (data.uuid && data.operate === 'resend') {
                if (this.iWasBlocked || this.isBeBlockInE2EE) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('message_blocked_error')
                    });
                    return;
                }

                console.log('operateMessageHandle', this.$refs[data.uuid]);
                if (data.chatComponentType === 'DOCUMENT') {
                    if (F.paths(data, 'plainMsg', 'm', 'meta', 'progress', 'type') === 'download') {
                        this.$refs[data.uuid]?.documentOperationHandle();
                    } else {
                        this.$refs[data.uuid]?.resetUploadHandle();
                    }
                } else if (data.chatComponentType === 'PICTURE') {
                    this.$refs[data.uuid]?.resetUploadHandle();
                } else {
                    this.reSendMessage(data.uuid);
                }
            }
        },
        async groupPinToTenDialogHandleClose(done = null) {
            this.$store.state.uiControl.groupPinTopToTenDialogVisible = false;
            done && Object.prototype.toString.call(done) == '[object Function]' && done();
        },
        async groupUnpinDialogHandleClose(done = null) {
            this.$store.state.uiControl.groupUnPinDialogVisible = false;

            done && Object.prototype.toString.call(done) == '[object Function]' && done();
        },
        reSizeInput(newH, type) {
            console.log('view height', newH);
            this.currentInputHeight = newH;
            // this.$refs.sendInputRef.style.height = `${newH}px`;
            // console.log('this.candown', this.candown);
            if (type === 'end') {
                const box = document.getElementById('rightContentPannelDom');
                // console.log('box: ', box);
                const header = box?.getElementsByClassName('dialog-head-container')[0];
                // console.log('header: ', header);

                if (box && header) {
                    const per = newH / (box.offsetHeight - header.offsetHeight);
                    appdataStorage.setItem('currentInputHeightPercent', per);
                }
            }

            if (!this.candown) {
                this.scrollBottom();
            }
        },
        async changeSessionDialogInfo(msg, spaceId) {
            let session = this.$store.state.sessionCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())];
            if (session) {
                await this.$store.dispatch('sessionCollection/changeSession', {
                    session: {
                        hid: session.hid,
                        lastReactTime: msg.m.stime,
                        lastMessageUUID: msg.m.uuid,
                        location: 33
                    },
                    spaceId
                });
            }
        },
        //重制当前聊天窗口的回复信息对象
        clearReplyPannel() {
            this.$store.commit('uiControl/setDialogReplyInfo', {
                replyPannelVisible: false,
                replyChatInfo: {},
                actDialogId: this.currentReadToken
            });
            this.getActiveReplyData();
        },
        //设置当前的消息聊天窗口的回复信息对象
        getActiveReplyData(actDialogId = this.currentReadToken) {
            this.currentDialogReplyInfo = this.$store.state.uiControl.replyCollection[actDialogId] || {
                replyPannelVisible: false,
                replyChatInfo: {},
                actDialogId: actDialogId
            };
        },
        //点击reply返回点击的那条信息
        replyHandler(replyChatInfo) {
            let currentDialogReplyInfo = {
                replyPannelVisible: true,
                replyChatInfo,
                actDialogId: this.currentReadToken
            };
            //设置当前的消息聊天窗口的回复信息对象
            this.$store.commit('uiControl/setDialogReplyInfo', currentDialogReplyInfo);
            this.getActiveReplyData();
            // auto focus
            this.$nextTick(() => {
                this.$refs.messageInputer.focusHandle();
            });
        },
        watchKeyChange() {
            if (this.$store.state.uiControl.verifyCodeDialogVisable) {
                this.$store.state.uiControl.needReRenderE2EEVerifyCode = true;
            }
        },
        // 在当前会话中，若无新消息，3秒后发送一次已读状态提高性能
        // 最大等待时间为5秒
        readStatusMultiSync: _.debounce(
            function () {
                if (this.mySession?.unreadCount) {
                    this.$store.dispatch('sessionCollection/changeSession', {
                        session: {
                            hid: this.mySession.hid,
                            unreadCount: 0,
                            readtime: this.mySession.lastReactTime ? this.mySession.lastReactTime + 1 : getTimestamp(),
                            location: 12
                        },
                        spaceId: defaultSpaceId()
                    });
                    userMessage({
                        conversation: dataUtil.hidToNumber(this.hid) + '|' + dataUtil.hidToNumber(this.$store.state.uiControl.actDialogId),
                        read: 1
                    }).catch(async function (error) {
                        if (error && error.responseHeader) {
                            msgDevLog.error('scrollHandle user/message:', sessionStorage.getItem('freelogin'), error);
                            if (error.responseHeader.status === 403 && sessionStorage.getItem('freelogin')) {
                                await forceCloseHandle('', `scrollHandle user/message 403close`);
                            }
                        }
                    });
                }
            },
            1000,
            {maxWait: 2000}
        ),
        async checkE2EEDeviceMap() {
            let actDialogId = this.$store.state.uiControl.actDialogId;
            let currentPeer = this.$store.state.peerCollection[enCodeSpaceHid(actDialogId, personalSpaceId())];
            if (currentPeer && dataUtil.getPeerType(actDialogId) == 'user' && this.getCurrentSpaceType) {
                msgDevLog.log('[no e2eeDeviceMap][checkE2EEDeviceMap ]', currentPeer.hid, currentPeer.e2eDeviceMap, personalSpaceId());
                if (!currentPeer.e2eDeviceMap) {
                    this.isE2EFaile = true;
                    // need retry scan and pin
                    await this.retryE2EEScan();
                    currentPeer = this.$store.state.peerCollection[enCodeSpaceHid(actDialogId, personalSpaceId())];
                    if (currentPeer.e2eDeviceMap) {
                        this.isE2EFaile = false;
                    }
                    clearTimeout(this.E2EEScanTimer);
                    this.E2EEScanTimer = window.setTimeout(() => {
                        currentPeer = this.$store.state.peerCollection[enCodeSpaceHid(actDialogId, personalSpaceId())];
                        if (!currentPeer.e2eDeviceMap && dataUtil.getPeerType(actDialogId) == 'user') {
                            console.error('[error]: ', 'scan retry error');
                            this.isE2EFaile = true;
                        }
                    }, 6000);
                }
            }
        },

        async retryE2EEScan() {
            let actDialogId = this.$store.state.uiControl.actDialogId;
            let selfHid = this.$store.state.userInfo.hid;

            msgDevLog.warn('[no e2eeDeviceMap][checkE2EEDeviceMap  retryE2EEScan]', actDialogId);
            let vuexPeer = this.$store.state.peerCollection[enCodeSpaceHid(actDialogId, personalSpaceId())];
            const uid_buffer = Buffer.from(selfHid, 'base64').reverse(); // BE to LE
            const uid = uid_buffer.readBigInt64LE().toString();
            const currentPeerFromServe = await getContactSimpleInfoList([actDialogId], 1, personalSpaceId());
            let bundleKeyMap = {};
            Object.keys(currentPeerFromServe).map(peerHid => {
                let peer = currentPeerFromServe[peerHid];
                if (peer.detail.keys && Object.keys(peer.detail.keys).length > 0) {
                    if (selfHid == actDialogId && peer.detail.keys.web) {
                        delete peer.detail.keys.web;
                    }

                    if (selfHid == actDialogId && peer.detail.keys.desktop) {
                        delete peer.detail.keys.desktop;
                    }

                    const peer_uid_buffer = Buffer.from(peerHid, 'base64').reverse(); // BE to LE

                    const peerUid = peer_uid_buffer.readBigInt64LE().toString();

                    peer.uid = peerUid;

                    bundleKeyMap[peerHid] = peer;
                }
            });
            const successSet = await sendSdk('E2EE-Help-Method', 3, bundleKeyMap);
            await this.$store.dispatch('uiControl/addPingList', {
                successSet,
                uid
            });

            msgDevLog.log('[no e2eeDeviceMap][retryE2EEScan ]', selfHid, successSet[actDialogId], personalSpaceId());
            await this.$store.dispatch('peerCollection/peerSetContactItem', {
                peers: [
                    {
                        ...vuexPeer,
                        e2eDeviceMap: successSet[actDialogId]
                    }
                ],
                spaceId: personalSpaceId()
            });
            E2EEPingUtil.addSendPingTask();
        },
        inview(uuid) {
            let el = document.getElementById(uuid);
            if (el) {
                let isInside = F.isInOfViewport(el, this.$refs.container_view.$el);
                return isInside == false;
            } else {
                return true;
            }
        },
        meetSinglePure(info) {
            this.clearContactCard();
            startCallMeeting(info);
            this.contactCard.visible = false;
        },
        autoVisible() {
            if (this.newMessagesNumer == 0) {
                this.visibleArrowUp = false;
                return;
            } else if (this.newMessagesUUID == null) {
                this.visibleArrowUp = false;
                this.newMessagesNumer = 0;
            } else {
                let el = document.getElementById(this.newMessagesUUID);
                if (el == null) {
                    this.visibleArrowUp = true;
                } else {
                    // Need a tool of timing logic, first same as here
                    setTimeout(() => {
                        let isInside = F.isInOfViewport(el, this.$refs.container_view.$el);
                        this.visibleArrowUp = isInside == false;
                        // },500) // 不知道为什么延迟 500 可能会影响滚动到底部 at 新消息 未读消息右下角部分异常。
                    }, 0);
                }
            }
        },

        renderTimeTip() {
            const timeTipDomList = document.getElementsByClassName('time_tip_box');
            if (timeTipDomList.length == 0) {
                let date = dataUtil.prettyTime(getTimestamp(), true);

                this.recentTime = (date || '').split(' ')[0];
                return;
            }
            for (let index = 0; index < timeTipDomList.length; index++) {
                const element = timeTipDomList[index];
                let prevTimeTipDomList = timeTipDomList[index - 1];
                let currentTop = element.getBoundingClientRect().top;
                if (currentTop >= 155 && prevTimeTipDomList) {
                    this.recentTime = prevTimeTipDomList.innerText;
                    break;
                }
                let lastTimeTipDomList = timeTipDomList[timeTipDomList.length - 1];
                let lastTop = lastTimeTipDomList.getBoundingClientRect().top;
                if (lastTimeTipDomList && lastTop < 155) {
                    this.recentTime = lastTimeTipDomList.innerText;
                    break;
                }
            }
        },
        async takeFirstUUIDUnread(n) {
            if (n == 0) {
                this.newMessagesUUID = null;
            } else {
                let uuid = await Peer.takeUUIDOrder(n, this.currentReadToken);
                this.newMessagesUUID = uuid;
            }
            // console.log('takeFirstUUIDUnread');
        },
        clearContactCard() {
            this.recipientCardObj.showValue = false;
        },
        chatNow(peer) {
            // console.log('debugOpenChatPage chatNow', peer);
            this.clearContactCard();
            Caught.errorWith(async () => {
                await Peer.assertWhatBeNotDeleted(peer.hid);
                await this.switchOne({hid: peer.hid, spaceId: defaultSpaceId()});
                this.contactCard.visible = false;
            });
            if (this.typeNameMembersToBeForward === 'receiptCard') fireReceiptsListChat();
        },
        addToContact(peer) {
            this.clearContactCard();
            Caught.errorWith(async () => {
                let spaceId = defaultSpaceId();
                let a = await Peer.addFriend(peer.hid, {
                    name: peer.name,
                    email: dataUtil.showEmailAndAccount(peer.email),
                    spaceId,
                    vm: this
                });
                if (a == 'Black') {
                    F.unblockThisPerson(Peer, Caught, this.contactCard.peer.hid);
                } else {
                    this.contactCard.visible = false;
                    this.$message({
                        message: this.$t('right_content_panel.add_to_concat_message'),
                        type: this.$t('right_content_panel.add_to_concat_success')
                    });
                }
            });
        },
        async reSendMessage(ruuid, isAuto) {
            Caught.errorWith(
                async () => {
                    F.checkCurrentSpaceExpire();
                    let place = -1;
                    this.viewChatList = this.viewChatList.filter(({uuid}, i) => {
                        if (uuid != ruuid) {
                            return true;
                        } else {
                            place = i;
                            return false;
                        }
                    });
                    if (place != -1) {
                        let new1 = this.viewChatList[place];
                        let new2 = this.viewChatList[place - 1] || {stime: 0};
                        if (new1 && new2) {
                            F.timeLogic(new1, new2);
                            F.dateLogic(new1, new2);
                        }
                    }
                    // 重发需要把@带上
                    let forword = F.forword(ruuid, false, undefined, true);
                    //表示转发是否需要回复消息
                    await forword(this.actDialogId, true);
                    await Peer.deleteAMessage(ruuid, defaultSpaceId());
                    this.$nextTick(() => {
                        this.setLimitUserSelectCheckbox('a5');
                    });
                },
                {loading: isAuto ? 'custom' : 'full'}
            );
        },
        // 已读列表
        async clickRecipient(event, reamember) {
            const {readmembers, unreadmembers, readcnt, unreadcnt} = reamember;
            const {pageY, pageX, offsetY} = event;
            let {groupUserId, groupPeerInfo} = await this.getGroupPeerList(readmembers, unreadmembers);
            this.recipientCardObj = {
                showValue: true,
                readmembers,
                unreadmembers,
                readNum: readcnt,
                unreadNum: unreadcnt,
                pageY,
                pageX,
                offsetY,
                groupUserId,
                groupPeerInfo
            };
            // console.log('clickRecipient', this.recipientCardObj, reamember);
        },
        // 更新群信息
        async getGroupPeerList(readmembers, unreadmembers) {
            if (dataUtil.getPeerType(this.actDialogId) == 'group') {
                let spaceId = defaultSpaceId();
                let groupHidList = await getGroupUidList(this.actDialogId, spaceId);
                let readmembersArr = await mangePeerList(spaceId, readmembers, 0);
                let unreadmembersArr = await mangePeerList(spaceId, unreadmembers, 0);
                let groupPeerInfo = {};
                readmembersArr.forEach(item => {
                    groupPeerInfo[item.hid] = item;
                });
                unreadmembersArr.forEach(item => {
                    groupPeerInfo[item.hid] = item;
                });
                let groupUserId = {};
                groupHidList.friends.forEach(item => {
                    groupUserId[item] = true;
                });
                return {groupUserId, groupPeerInfo};
            }
        },
        doubleContactCard(peerInfo) {
            if (peerInfo?.hid == this.$store.state.userInfo.hid) return;
            if (!this.isGroup) return;
            this.$refs.messageInputer?.doubleContactCard(peerInfo);
        },
        // 详情信息卡片
        async clickContactInfo(el, hid) {
            this.showContactInfo(hid, el);
        },
        // 回复列表中点开信息卡片
        showContactInfoByRecipientCard(id, e) {
            this.typeNameMembersToBeForward = 'receiptCard';
            this.showContactInfo(id, e, 1);
        },
        // 信息卡片
        async showContactInfo(id, e, type) {
            // console.log('showContactInfo', id, this.contactCard);
            // if (id === this.contactCard.peer?.hid) return;
            // group pin 不需要点击显示名片
            if (this.$store.state.uiControl.groupPinPannelVisible) return;
            // Meeting Assistant not show card
            if (dataUtil.getIsWhiteList(id)) return;
            // const hostId = await getHid();
            // if (id === this.hid) return;
            let spaceId = defaultSpaceId();
            let peer;
            // 缓存信息
            const peerCache = await mangePeerList(spaceId, [id], 0);
            if (peerCache.length) {
                peer = peerCache[0];
                this.contactCard.who = (await Peer.areWeFriend(id)) ? 1 : 0;
                if ((peer && Object.keys(peer).length === 0) || !peer || (!peer.email && !peer.phoneNumber)) {
                    peer.isEmptyUser = true;
                }
                this.contactCard.peer = peer;
                this.contactCard.visible = true;
                this.$nextTick(() => {
                    this.setConcatPopoverPostion(e, {type: 'showContactInfo'});
                });
            }
            peer = await Peer.fetchWholeForContact(id, spaceId, 0, true);

            if (this.contactCard.peer.hid === peer.hid || !peer.hid) {
                // 实时信息
                // 用户是否存在该空间
                let isExistSpace = await getEnterpriseMember(spaceId, id);

                if ((peer && Object.keys(peer).length === 0) || !peer) {
                    if (!peerCache.length) {
                        this.contactCard.peer = {isEmptyUser: true, isExistSpace};
                    }
                } else {
                    this.contactCard.peer = {...peer, isExistSpace};
                }

                this.contactCard.who = (await Peer.areWeFriend(id)) ? 1 : 0;
                // 如果回复列表已经关闭，则不显示名片
                if (type === 1 && !this.recipientCardObj.showValue) {
                    return;
                }
                // this.contactCard.visible = true;
                // 等待加载完成才能获取到真实的宽高
                this.$nextTick(() => {
                    this.setConcatPopoverPostion(e, {type: 'showContactInfo'});
                    this.$store.commit('dialogList/makeItReact', {
                        dialogItem: {
                            hid: id,
                            spaceId
                        },
                        spaceId,
                        location: 1
                    });
                });
            }
        },
        setConcatPopoverPostion(element, {type} = {}) {
            const concatRef = this.$refs.ConcatPopover.getPopoverContactRef();
            const popoverContact = concatRef.$refs.popper;

            const rect = element.getBoundingClientRect();
            const sessionWidth = appdataStorage.getItem('currentSessionWidth') || 296;
            const leftWidth = sessionWidth + 80;

            // popoverContact.style.left = `${rect.x + 38 - leftWidth}px`;
            let {width: bodyWidth, height: bodyHeight} = window.getComputedStyle(document.body);
            let {width: popoverWidth, height: popoverHeight} = window.getComputedStyle(popoverContact);
            bodyHeight = Number(bodyHeight.replace('px', ''));
            bodyWidth = Number(bodyWidth.replace('px', ''));
            popoverWidth = Number(popoverWidth.replace('px', ''));
            popoverHeight = Number(popoverHeight.replace('px', ''));
            const marginGutter = 12; // 距离底部/和右边12px

            if (this.$i18n.locale === 'ar') {
                if (rect.x + popoverWidth + rect.width + marginGutter < bodyWidth - leftWidth) {
                    popoverContact.style.left = `${element.offsetLeft - popoverWidth}px`;
                } else {
                    // 贴近右边,距离右边marginGutterpx
                    popoverContact.style.left = `${bodyWidth - leftWidth - popoverWidth - marginGutter}px`;
                }
            } else {
                if (rect.x - leftWidth + popoverWidth + rect.width + marginGutter < bodyWidth - leftWidth) {
                    // popoverContact.style.left = `${rect.x + rect.width - leftWidth}px`;
                    popoverContact.style.left = `${element.offsetLeft + rect.width}px`;
                } else {
                    // 贴近右边,距离右边marginGutterpx
                    popoverContact.style.left = `${bodyWidth - leftWidth - popoverWidth - marginGutter}px`;

                    // popoverContact.style.left = `${rect.x - leftWidth - popoverWidth}px`;
                }
            }
            if (rect.y + popoverHeight + marginGutter + 56 > bodyHeight) {
                popoverContact.style.top = `${bodyHeight - popoverHeight - marginGutter - 56}px`;
            } else {
                popoverContact.style.top = `${type === 'viewAtClick' ? rect.y - 20 : rect.y}px`;
            }
        },
        // 通用显示信息卡片
        async viewAtClick(hid, el, member, isEmptyUser) {
            // const hostId = await getHid();
            if (hid === this.hid) {
                return;
            }
            if (
                (hid == this.$store.state.userInfo.hid && (!member || member?.enterpriseId === defaultSpaceId())) ||
                dataUtil.getPeerType(hid) == 'group'
            ) {
                return;
            }
            // group pin 不需要点击显示名片
            if (this.$store.state.uiControl.groupPinPannelVisible) {
                return;
            }
            Caught.errorWith(async () => {
                let peer;
                if (member?.isExternal) {
                    peer = {
                        ...member
                    };
                    this.contactCard.who = 1;
                } else {
                    this.contactCard.who = (await Peer.areWeFriend(hid)) ? 1 : 0;
                    peer = await Peer.getSinglePeerByHid(hid);
                    if (member) {
                        peer = {
                            ...peer,
                            ...member
                        };
                    }
                }

                this.contactCard.visible = true;
                this.contactCard.peer = isEmptyUser ? {} : peer;
                // 等待加载完成才能获取到真实的宽高
                this.$nextTick(() => {
                    this.setConcatPopoverPostion(el, {type: 'viewAtClick'});
                });
            });
        },
        copyMeetingInviteMessageHandler() {
            let {meta} = this.currentChatInfo.plainMsg.m;
            copyMetaInvitation(meta);
        },
        async sendMeetingInvite(meetingInfo) {
            // console.log('**************sendMeetingInvite*******************');
            let msgTemplate = new MsgTemplate();
            let dialogId = this.$store.state.uiControl.actDialogId;
            let parm = msgTemplate.setList(msgTemplate.getInviteTemplate(dialogId, meetingInfo));
            // 仿造消息
            await handleMessage(parm);
            // 发送消息
            this.$emit('sendMsgFromPannel', {
                payload: parm,
                cmd: 'HyperText'
            });
        },
        // closeMemberDialog() {
        //     this.openedMembersToBeForward = false;
        //     this.$refs.vuetobeadded?.resetData();
        //     this.$refs.vuetobeadded?.resetSearchData(true);
        // },
        // closeNameCardDialog() {
        //     this.namecardDialogVisable = false;
        //     this.$nextTick(() => {
        //         this.$refs?.nameCardDialogRef?.resetData();
        //         this.$refs?.nameCardDialogRef?.resetSearchData(true);
        //     });
        // },
        // 转发文件消息
        forwardDocumentMessage() {
            this.$nextTick(() => {
                this.$refs.vuetobeadded?.resetData();
                this.$refs?.vuetobeadded?.resetSearchData(true);
            });
            this.typeNameMembersToBeForward = 'forwardSelect';
            // this.openedMembersToBeForward = true;
            this.openDialogCheckList('forwardMessage');
        },
        // 转发文本消息
        forwardTextMessage(info) {
            this.$nextTick(() => {
                if (info) {
                    this.currentChatInfo = info;
                }
                this.$refs?.vuetobeadded?.resetData();
                this.$refs?.vuetobeadded?.resetSearchData(true);
            });
            this.typeNameMembersToBeForward = 'forwardSelect';
            // this.openedMembersToBeForward = true;
            this.openDialogCheckList('forwardMessage');
        },
        // 删除文件消息
        async deleteDocumentMessage(options = {}) {
            // console.error('options :>> ', options);
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                // 上传中先cancel
                this.deleteTextMessage(async () => {}, options);
            });
        },
        // 删除文件消息 不弹窗
        async deleteDocumentMessageNoDialog(options = {}) {
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                // 上传中先cancel
                this.deleteTextMessageNoDialog(async () => {}, options);
            });
        },
        // e2e 创建会话失败
        async faileE2ESessionHandle() {
            let currentName = this.nickName(
                this.$store.state.peerCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, personalSpaceId())]
            );
            this.$confirm(
                this.$t('right_content_panel.fail_e2ee_session_message', [currentName]),
                this.$t('right_content_panel.fail_e2ee_session_title'),
                {
                    confirmButtonText: this.$t('right_content_panel.fail_e2ee_session_confirm_button'),
                    cancelButtonText: this.$t('right_content_panel.right_click_panel_cancel'),
                    customClass: 'myconfirm',
                    showClose: false
                }
            ).then(async () => {
                this.$store.state.uiControl.openReportingDialog = true;
            });
        },
        async doRealDeleteMsg(callFn, options) {
            // 撤回消息后的替换消息流的操作
            const ackFinishFn = async function (sendTo, spaceId, message) {
                // 替换这条信息
                let run = insteadMessage(this.currentChatInfo.uuid, spaceId);
                await run();

                let key = 'newmsg__' + sendTo;
                if (window[key]) window[key]();

                // Send the withdrawal message to the picture viewer
                // Notification Picture Viewer Delete Pictures View
                sendPictureViewerWithdraw({uuid: message.m.uuid, dialogId: message.t}, message);
                deleteTempMessage(message.m.uuid, spaceId);
                let subType =
                    (this.currentChatInfo.filelisttype && this.currentChatInfo.filelisttype == 3) || !this.currentChatInfo.filelisttype
                        ? SearchEnum.ftsType.BUS_SUBTYPE_MESSAGE
                        : SearchEnum.ftsType.BUS_SUBTYPE_FILE;

                FtsService.delete({
                    spaceId: spaceId,
                    type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                    subType: subType,
                    busItemId: this.currentChatInfo.uuid
                });
            };

            if (options && options.documentUploadFn) {
                options.documentUploadFn();
            }

            if (options && options.onlyLocal) {
                let ruuid = this.currentChatInfo.uuid;
                let place = -1;
                this.viewChatList = this.viewChatList.filter(({uuid}, i) => {
                    if (uuid != ruuid) {
                        return true;
                    } else {
                        place = i;
                        return false;
                    }
                });
                if (place != -1) {
                    let new1 = this.viewChatList[place];
                    let new2 = this.viewChatList[place - 1] || {stime: 0};
                    if (new1 && new2) {
                        F.timeLogic(new1, new2);
                        F.dateLogic(new1, new2);
                    }
                }
                await Peer.deleteAMessage(ruuid, defaultSpaceId());
                let subType =
                    (this.currentChatInfo.filelisttype && this.currentChatInfo.filelisttype == 3) || !this.currentChatInfo.filelisttype
                        ? SearchEnum.ftsType.BUS_SUBTYPE_MESSAGE
                        : SearchEnum.ftsType.BUS_SUBTYPE_FILE;

                FtsService.delete({
                    spaceId: defaultSpaceId(),
                    type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                    subType: subType,
                    busItemId: ruuid
                });

                this.$nextTick(() => {
                    this.setLimitUserSelectCheckbox('a6');
                });
                return;
            }
            // 发送withdraw消息
            let sendTo = this.$store.state.uiControl.actDialogId;
            let msgTemplate = new MsgTemplate();
            let parm = msgTemplate.setList(msgTemplate.getMsgTemplate(sendTo, '', []));
            parm = msgTemplate.renderWithDrawMsg(parm, this.currentChatInfo.uuid);
            // 发送消息
            let [produce] = F.globalConsume();
            let message = parm;
            let spaceId = getSpaceId(message.mcTo);
            // let originUuid = message.m.uuid;
            // let tUuid = (originUuid || '').replace('withdraw|', '');

            if (
                this.currentChatInfo.isFail &&
                this.currentChatInfo.plainMsg?.meta?.isEditTemp &&
                this.currentChatInfo.plainMsg?.progress?.uploadOriginFilePath
            ) {
                console.log(
                    `🚀 ~ doRealDeleteMsg ~ 'DELETE_FILE':`,
                    'DELETE_FILE',
                    this.currentChatInfo.plainMsg.progress.uploadOriginFilePath
                );
                ipcRenderer.send('DELETE_FILE', this.currentChatInfo.plainMsg.progress.uploadOriginFilePath);
            }

            // 如果是普通发送失败消息，直接删除，不需要发消息给服务器
            if (this.currentChatInfo.isFail && this.currentChatInfo.plainMsg?.m?.MIMETYPE == 'text/plain') {
                let needStoreMssage = parm;
                needStoreMssage.m.uuid = this.currentChatInfo.uuid;
                needStoreMssage.filelisttype = null;

                await this.$store.dispatch('messageCollection/saveMessage', {
                    message: parm
                });

                ackFinishFn.call(this, sendTo, spaceId, message);
            } else {
                produce(
                    message.m.uuid,
                    async isFail => {
                        if (isFail) {
                            this.$message(this.$t('right_content_panel.delete_message_fail'));
                        } else {
                            ackFinishFn.call(this, sendTo, spaceId, message);
                        }
                    },
                    2000
                );

                this.$emit('sendMsgFromPannel', {
                    payload: parm,
                    cmd: 'HyperText'
                });

                if (typeof callFn == 'function') {
                    callFn();
                }

                let needStoreMssage = parm;
                needStoreMssage.m.uuid = this.currentChatInfo.uuid;
                needStoreMssage.filelisttype = null;

                await this.$store.dispatch('messageCollection/saveMessage', {
                    message: parm
                });
            }
        },
        // 删除文本消息不弹框
        async deleteTextMessageNoDialog(callFn, options) {
            await this.doRealDeleteMsg(callFn, options);
        },
        // 删除文本消息
        async deleteTextMessage(callFn, options) {
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                this.$confirm(this.$t('right_content_panel.delete_text_message'), this.$t('right_content_panel.delete_text_title'), {
                    confirmButtonText: this.$t('right_content_panel.right_click_panel_delete'),
                    cancelButtonText: this.$t('right_content_panel.right_click_panel_cancel'),
                    customClass: 'myconfirm',
                    showClose: false
                }).then(() => {
                    this.doRealDeleteMsg(callFn, options);
                });
            });
        },
        // delete meet card
        async deleteMeetCardMessage(callFn, options) {
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                this.$confirm(this.$t('meetCard.meetingImpact'), this.$t('right_content_panel.delete_text_title'), {
                    confirmButtonText: this.$t('right_content_panel.right_click_panel_delete'),
                    cancelButtonText: this.$t('right_content_panel.right_click_panel_cancel'),
                    customClass: 'myconfirm',
                    showClose: false
                }).then(() => {
                    this.doRealDeleteMsg(callFn, options);
                });
            });
        },
        // async sendNamecardHandle(members) {
        //     fireNameCardSend();
        //     const ids = members.map(member => member.id);
        //     if (ids.length <= 0) {
        //         return;
        //     }
        //     if (this.iWasBlocked) {
        //         this.$message({
        //             type: 'warning',
        //             message: this.$t('message_blocked_error')
        //         });
        //         return;
        //     }
        //     try {
        //         F.checkCurrentSpaceExpire();
        //         await Peer.checkIfsendMsg(this.$store.state.uiControl.actDialogId);
        //     } catch (error) {
        //         if (error && error.msg) {
        //             this.$message({
        //                 message: error.msg,
        //                 type: 'error'
        //             });
        //         }
        //         return;
        //     }
        //     let curPeer = members[0];
        //     let curHid = ids[0];

        //     let sendTo = this.$store.state.uiControl.actDialogId;
        //     let nameCardInfo = {
        //         contactUid: dataUtil.hidToUid(curHid),
        //         nickName: curPeer.name
        //     };
        //     let msgTemplate = new MsgTemplate();
        //     let msg = msgTemplate.getNameCardTemplate(sendTo, nameCardInfo);
        //     let parm = msgTemplate.setList(msg);
        //     parm.fromLocal = true;
        //     let needE2EMessage = _.merge({}, parm);

        //     // 仿造消息
        //     await handleMessage(needE2EMessage);
        //     // 发送消息
        //     this.$emit('sendMsgFromPannel', {
        //         payload: needE2EMessage,
        //         cmd: 'HyperText'
        //     });
        //     this.$message({
        //         message: this.$t('right_content_panel.namecard_message_success'),
        //         type: 'success'
        //     });
        //     this.closeNameCardDialog();
        // },
        // 转发
        eventMembersToBeForward(event, data) {
            const {uuid, isExistGroupPinList, plainMsg, memberId} = data;
            console.log('SEND-CURRENT-WIN-MSG--', uuid, isExistGroupPinList, plainMsg, memberId);
            const handler = F.forwords(uuid, isExistGroupPinList, plainMsg, memberId);
            const queueManager = this.queueManager;
            queueManager
                .push({
                    uuid: memberId, // 自动根据根据 memberId 分组
                    fetch: handler.bind(this, [memberId])
                })
                .then(() => {})
                .catch(error => {
                    console.error('[error]: ', 'error :>> ', error);
                });
        },
        async gotMembersToBeForward(members) {
            try {
                console.time('getMembersToBeForward--');
                F.checkCurrentSpaceExpire();

                let viewChatCheckedList = this.viewChatCheckedList;
                const isMultipleSelected = this.isMultipleSelected;
                const currentChatInfo = this.currentChatInfo;
                if (!isMultipleSelected) {
                    // 是否为多选状态
                    viewChatCheckedList = [currentChatInfo];
                }
                // 限制发送消息的顺序
                // 逐条转发消息管理
                // const queueManager = new QueueManager(1);
                const queueManager = this.queueManager;

                for (const member of members) {
                    const memberId = member.id;
                    for (const item of viewChatCheckedList) {
                        const handler = F.forwords(item.plainMsg.uuid, item.isExistGroupPinList, item.plainMsg);
                        queueManager
                            .push({
                                uuid: memberId, // 自动根据根据 memberId 分组
                                fetch: handler.bind(this, [memberId])
                            })
                            .then(() => {})
                            .catch(error => {
                                console.error('[error]: ', 'error :>> ', error);
                            });
                    }
                }
                // 关闭group pin 面板
                this.closeGroupPannel();
                this.$message({
                    message: this.$t('right_content_panel.forward_message_success'),
                    type: 'success'
                });
                console.timeEnd('getMembersToBeForward--');
                if (members.length === 1) {
                    let dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
                    const st = setTimeout(() => {
                        this.$store.dispatch('uiControl/switchOne', {hid: members[0].id, spaceId: defaultSpaceId()});
                        window.clearTimeout(st);
                        requestAnimationFrame(() => {
                            if (dom) {
                                dom.scrollTop = 0;
                            }
                            dom = null;
                        });
                    }, 500);
                }
            } catch (error) {
                if (error && error.msg) {
                    this.$message({
                        message: error.msg,
                        type: 'error'
                    });
                }
                console.error('[error]: ', 'gotMembersToBeForward', error);
            } finally {
                this.$store.commit('uiControl/setMultipleSelected', false);
                // this.openedMembersToBeForward = false;
                this.$nextTick(() => {
                    if (!this.candown) {
                        requestAnimationFrame(() => {
                            let dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
                            dom && (dom.scrollTop = 0);
                            dom = null;
                        });
                    }
                    this.$refs?.vuetobeadded?.resetData();
                    this.$refs?.vuetobeadded?.resetSearchData(true);
                });
            }
        },
        async openToBeCombineForward() {
            let xx_vuid = appdataStorage.getItem('xx_vuid');
            const previewContent = this.viewChatCheckedList;
            console.log('CombineChat gotMembersToBeCombineForward', previewContent);
            const currentReadToken = this.$store.getters['uiControl/getActDialogId'];
            ipcRenderer.send('COMBINE_CHAT', {
                type: 'open',
                combineId: 'noCombineId',
                currentReadToken,
                combineList: previewContent.map(item => item.uuid),
                defaultSpaceId: defaultSpaceId(),
                xx_vuid: xx_vuid
            });
        },
        async gotMembersToBeCombineForward(members) {
            console.log('CombineChat gotMembersToBeCombineForward', members, this.viewChatCheckedList);
            try {
                console.time('getMembersToBeForward--');
                F.checkCurrentSpaceExpire();

                let viewChatCheckedList = this.viewChatCheckedList;
                const isMultipleSelected = this.isMultipleSelected;
                const currentChatInfo = this.currentChatInfo;
                if (!isMultipleSelected) {
                    viewChatCheckedList = [currentChatInfo];
                }
                const queueManager = this.queueManager;

                for (const member of members) {
                    const memberId = member.id;
                    const handler = F.forwordMerges(viewChatCheckedList);
                    queueManager
                        .push({
                            uuid: memberId, // 自动根据根据 memberId 分组
                            fetch: handler.bind(this, [memberId])
                        })
                        .then(() => {})
                        .catch(error => {
                            console.error('[error]: ', 'error :>> ', error);
                        });
                }
                this.closeGroupPannel();
                this.$message({
                    message: this.$t('right_content_panel.forward_message_success'),
                    type: 'success'
                });
                console.timeEnd('getMembersToBeForward--');
                if (members.length === 1) {
                    let dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
                    const st = setTimeout(() => {
                        this.$store.dispatch('uiControl/switchOne', {hid: members[0].id, spaceId: defaultSpaceId()});
                        window.clearTimeout(st);
                        requestAnimationFrame(() => {
                            if (dom) {
                                dom.scrollTop = 0;
                            }
                            dom = null;
                        });
                    }, 500);
                }
            } catch (error) {
                if (error && error.msg) {
                    this.$message({
                        message: error.msg,
                        type: 'error'
                    });
                }
                console.error('[error]: ', 'gotMembersToBeForward', error);
            } finally {
                this.$store.commit('uiControl/setMultipleSelected', false);
                // this.openedMembersToBeForward = false;
                this.$nextTick(() => {
                    if (!this.candown) {
                        requestAnimationFrame(() => {
                            let dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
                            dom && (dom.scrollTop = 0);
                            dom = null;
                        });
                    }
                    this.$refs?.vuetobeadded?.resetData();
                    this.$refs?.vuetobeadded?.resetSearchData(true);
                });
            }
        },
        // Save as...保存图片 保存文件
        async savePictureMessage() {
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                let filePath = this.currentChatInfo.plainMsg.assertPath;
                if (!filePath && this.$store.state.uiControl.replyListPannelVisible) {
                    filePath = await this.checkFileisExist(this.currentChatInfo);
                }
                if (!existFile(filePath)) {
                    this.$message(this.$t('errorCode.error_save_as'));

                    this.currentChatInfo.plainMsg.m.meta.progress.type = 'download';

                    this.currentChatInfo.plainMsg.m.meta.progress.sendStatus = '';
                    this.currentChatInfo.plainMsg.m.meta.progress.loaded = 0;

                    this.$store.commit('fileProcessCollection/updateFileProcess', {
                        dialogId: this.currentChatInfo.plainMsg.dialogId,
                        messageId: this.currentChatInfo.plainMsg.m.uuid,
                        progress: {
                            ...this.currentChatInfo.plainMsg.m.meta.progress
                        }
                    });

                    this.$store.dispatch('messageCollection/saveMessage', {message: this.currentChatInfo.plainMsg});

                    this.$store.commit('fileWorking/delFileWorkObj', this.currentChatInfo.plainMsg.m.uuid);

                    return;
                }

                filePath && ipcRenderer.send('SAVE-AS-PIC', filePath);
            });
        },
        // Save as...保存图片 保存文件
        async openFileLocationMessage() {
            try {
                let filePath = this.currentChatInfo.plainMsg.assertPath;
                if (!filePath && this.$store.state.uiControl.replyListPannelVisible) {
                    filePath = await this.checkFileisExist(this.currentChatInfo);
                }
                if (filePath && Tool.existFile(filePath)) {
                    ipcRenderer.send('OPEN-FILE-LOCATION', filePath);
                } else {
                    const plainMsg = this.currentChatInfo.plainMsg;

                    plainMsg.m.meta.progress.type = 'download';

                    this.$store.commit('fileCancelCollection/setFileCancelObj', {
                        uuid: plainMsg.m.uuid,
                        isCancel: false
                    });

                    plainMsg.m.meta.progress.sendStatus = '';
                    plainMsg.m.meta.progress.loaded = 0;

                    this.$store.commit('fileProcessCollection/updateFileProcess', {
                        dialogId: plainMsg.dialogId,
                        messageId: plainMsg.m.uuid,
                        progress: {
                            ...plainMsg.m.meta.progress
                        }
                    });

                    this.$store.dispatch('messageCollection/saveMessage', {message: plainMsg});

                    this.$store.commit('fileWorking/delFileWorkObj', plainMsg.m.uuid);
                }
            } catch (error) {
                console.error('OPEN-FILE-LOCATION error', error);
            }
        },
        // 取消block
        unBlock() {
            Caught.errorWith(async () => {
                await Peer.updateBlock(this.currentDialogPeerInfo.hid, false);
                this.currentDialogPeerInfo.isBeBlock = false;
            });
        },
        async mockSendMsgHandle(obj) {
            let lastMsg;
            let spaceId = defaultSpaceId();

            // 准备发送文件
            let fileMap = this.getFileObj;
            let fileSizeList = [];
            Object.keys(fileMap).map(fileItem => {
                fileSizeList.push({
                    size: fileMap[fileItem].size,
                    fileType: fileMap[fileItem].fileType,
                    file: fileMap[fileItem],
                    isEditTemp: fileMap[fileItem].isEditTemp
                });
            });
            let sendHidArr = this.$store.state.uiControl.sendHidArr;
            // 清空发送 @群成员 信息集合
            if (sendHidArr.length > 0) this.$store.commit('uiControl/setSendHidArr', []);
            // 清空文件缓存
            this.clearFileList();
            // 按照文件大小排序 小的在前面
            fileSizeList.sort((prev, next) => {
                return prev.size - next.size;
            });
            // 伪造发送文件消息
            for (let idx = 0; idx < fileSizeList.length; idx++) {
                const fileItem = fileSizeList[idx].file;
                // 模拟消息发送，左边会话短消息需发送成功后才显示。
                let fackMsg = await productMsg.call(this, fileItem);
                fackMsg = JSON.parse(fackMsg);
                await preprocess(fackMsg);

                devFileLog.info('[mockSendMsgHandle]', fackMsg);
                await this.$store.dispatch('messageCollection/saveMessage', {
                    message: fackMsg
                });

                lastMsg = fackMsg;
                if (window.newMsgComing) window.newMsgComing(fackMsg);
            }
            if (lastMsg) {
                try {
                    await this.changeSessionDialogInfo(lastMsg, spaceId);
                    // 需要在changePeer之後 否則獲取lastreactTime异常
                    this.$store.commit('dialogList/spliceDialog', {
                        dialogItem: {
                            hid: lastMsg.dialogId,
                            spaceId
                        },
                        index: dataUtil.getDialogIndex(spaceId, lastMsg, false),
                        spaceId
                    });
                } catch (error) {
                    console.error('[error]: ', error);
                }
                lastMsg = null;
            }
            // 发送普通消息 普通消息要在文件之后
            obj.payload.m.stime = getTimestamp() + 100;
            if (obj.payload && obj.payload.m && obj.payload.m.body) {
                this.$emit('sendMsgFromPannel', {
                    payload: obj.payload,
                    cmd: obj.cmd
                });
                // 普通消息和文件消息一起发送时，普通消息先发出去
                // 仿照新消息，接受消息
                await preprocess(obj.payload);
                await this.changeSessionDialogInfo(obj.payload, spaceId);

                // 已读回执初始化文件
                let isReceipt = true;
                if (obj.payload.m.stime) isReceipt = obj.payload.m.stime > appdataStorage.getItem('RECEIPT_START_TIME');
                if (!obj.payload.unreadReceipt) obj.payload.unreadReceipt = isReceipt ? '0' : null;

                await this.$store.dispatch('messageCollection/saveMessage', {
                    message: obj.payload
                });
                this.$store.commit('dialogList/spliceDialog', {
                    dialogItem: {
                        hid: this.$store.state.uiControl.actDialogId,
                        spaceId
                    },
                    index: dataUtil.getDialogIndex(spaceId, obj.payload, false),
                    spaceId
                });
                devFileLog.info('[mockSendMsgHandle][plainmsg]', obj.payload);

                // 发消息时 清除@消息 里面有判断没有不做清除操作数据库
                // this.$store.commit('sessionCollection/remindChange', {
                //     peerHid: this.$store.state.uiControl.actDialogId,
                //     spaceId
                // });
                this.$store.commit('uiControl/setMetioned', []);

                if (window.newMsgComing) window.newMsgComing(obj.payload);
            }
        },
        filterRightOptions(rightPanelOptions, info, source) {
            if (!Array.isArray(rightPanelOptions)) {
                console.error('rightPanelOptions is not an array');
                return [];
            }

            if (!info) {
                console.error('info is empty');
                return [];
            }

            if (this.isDebug) {
                rightPanelOptions.unshift({
                    label: 'Debug Copy',
                    type: 'debug',
                    handler: this.debugCopy
                });
            }

            // Only forward & copy
            if ((this.isSubscribe || dataUtil.getIsWhiteList(info.peerId)) && info.chatComponentType !== 'MEETINGCARD') {
                rightPanelOptions = rightPanelOptions.filter(({type} = {}) => ['forward', 'copy'].includes(type));
            }

            // Remove emoji
            if (source === 'TouchBar') {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'add_emoji');
            }

            // Remove delete
            if (info.peerId !== this.$store.state.userInfo.hid && info.chatComponentType !== 'MEETINGCARD') {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'delete');
            }

            // Remove retry
            if (!info.isFail) {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'retry');
            }

            // Remove select & forward & reply
            if (info.isFail || info.isSending) {
                rightPanelOptions = rightPanelOptions.filter(item => !['select', 'forward', 'reply'].includes(item.type));
            }

            // Remove join_meeting
            if (this.meetingStatus === 'pending') {
                rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'join_meeting');
            }

            // Remove pin / unpin
            if (this.isGroup) {
                const isUnpin = info?.plainMsg?.pinnedInfo?.operation || info?.plainMsg?.m?.meta?.pinnedInfo?.pfrom;
                rightPanelOptions = rightPanelOptions.filter(item => (isUnpin ? item.type !== 'pin' : item.type !== 'unpin'));
            } else {
                rightPanelOptions = rightPanelOptions.filter(item => !['pin', 'unpin'].includes(item.type));
            }

            if (info.plainMsg?.MIMETYPE === 'poi/card') {
                // 如果是地图卡片 Copy，Forward，Select 不显示
                rightPanelOptions = rightPanelOptions.filter(
                    item => !(item.type === 'copy' || item.type === 'select' || item.type === 'forward')
                );
                const item = {
                    label: this.$t('map.open_new_tab'),
                    type: 'open_win',
                    handler: this.openPoiWin
                };
                const index = rightPanelOptions.findIndex(ele => ele.type === 'driver');

                if (index !== -1) {
                    rightPanelOptions.splice(index + 1, 0, item);
                }
            }

            // Remove edit
            if (info.chatComponentType === 'MESSAGE' || info.chatComponentType === 'MESSAGELONG') {
                // Filter editing function
                // Messages sent by oneself, and within 15 minutes.
                const canEdit =
                    info.peerId === this.$store.state.userInfo.hid &&
                    this.editMessageState.uuid !== info.uuid &&
                    (getTimestamp() - info.stime < 1000 * 60 * 15 || info.isFail);
                if (!canEdit) {
                    rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'edit');
                }
            }

            // Remove translate / hide_translate
            if (info.chatComponentType === 'MESSAGE' || info.chatComponentType === 'MESSAGELONG' || info.chatComponentType === 'RICHTEXT') {
                // Filter translate / hide translate option
                const canTranslate = this.checkCanTranslate(info.plainMsg);
                if (this.isUseMessageTranslation && canTranslate) {
                    if (info?.plainMsg?.extraData?.translate) {
                        rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'translate');
                    } else {
                        rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'hide_translate');
                    }
                } else {
                    rightPanelOptions = rightPanelOptions.filter(item => item.type !== 'hide_translate' && item.type !== 'translate');
                }
            }

            // Remove comment & copy
            if (info.chatComponentType === 'Announcement' && info?.plainMsg?.m?.meta?.announcementInfo?.delete) {
                rightPanelOptions = rightPanelOptions.filter(item => !['comment', 'copy'].includes(item.type));
            }

            // Remove comment
            if (info.chatComponentType === 'MESSAGE' && info?.plainMsg?.m?.meta?.announcementInfo?.delete) {
                rightPanelOptions = rightPanelOptions.filter(item => !['comment'].includes(item.type));
            }

            // Filter driver
            rightPanelOptions = rightPanelOptions.reduce((result, item, index, arr) => {
                if (item.type === 'driver') {
                    const preElement = result[result.length - 1];
                    const isNotLastElement = !(index === arr.length - 1 && item.type === 'driver');
                    if (result.length > 0 && preElement.type !== 'driver' && isNotLastElement) {
                        result.push(item);
                    }
                } else {
                    result.push(item);
                }
                return result;
            }, []);

            return rightPanelOptions;
        },
        debugCopy(info) {
            navigator.clipboard.writeText(JSON.stringify(info));
            this.$message.success(this.$t('right_content_panel.copySuccess'));
        },
        async checkFileisExist(info) {
            let uuid = info.plainMsg.m.uuid;
            let message = await getWith('select * from message where uuid = :uuid', {uuid}, defaultSpaceId());
            let filePath = message && message.assertPath;
            return filePath;
        },
        // 是否支持转发
        isSupportedForwards(info) {
            if (!this.ALL_RIGHT_OPTIONS) {
                this.ALL_RIGHT_OPTIONS = this.RIGHT_CLICK_OPTIONS();
            }
            const sendStatus = F.paths(info, 'plainMsg', 'm', 'meta', 'progress', 'sendStatus');
            let status = sendStatus || 'default';

            let options = status && F.paths(this.ALL_RIGHT_OPTIONS, info.chatComponentType, status);

            if (info.isExistGroupPinList && F.paths(this.ALL_RIGHT_OPTIONS, info.chatComponentType, 'download')) {
                options = F.paths(this.ALL_RIGHT_OPTIONS, info.chatComponentType, 'download');
            }

            if (!Array.isArray(options)) {
                options = [];
            }

            const index = options.findIndex(item => item.type === 'select');
            return index !== -1 && !info.isFail && !info.isSending;
        },
        // 点击右键，打开选项
        async openRightPanel($event, info, source) {
            console.log('openRightOption: ', info, source);
            if (F.isLocationShare(info.plainMsg)) {
                // 如果是共享实时位置 不显示
                return;
            }

            if ($event.target.classList.contains('message-bar-content')) {
                return;
            }
            // 停止播放
            Bus.$emit('voice-play', '');
            this.rightPanelOptions = [];
            this.currentChatInfo = info;
            // 定位选项box到鼠标右键处
            // 280 - 左侧sessionlist定宽
            // 61 - 顶部定高
            // 10 - 增加偏移20 避免激活时误操作

            const sessionWidth = appdataStorage.getItem('currentSessionWidth') || 296;
            const leftMenuWidth = 64;

            let maxLeft = window.innerWidth - 170 - 330 - 64 + 20;
            this.tranLeft = $event.pageX - 330 + 10 > maxLeft ? maxLeft - 120 : $event.pageX - 330 + 10;
            if (this.$i18n.locale === 'ar')
                this.tranLeft = $event.pageX - 330 + 10 > maxLeft ? maxLeft - 120 + 150 : $event.pageX - 330 + 150 + 10;
            this.tranTop = $event.pageY - 91 + 10;
            this.tranRight = 'auto';

            if (source === 'TouchBar') {
                try {
                    const touchBarEle = $event.target.closest('.touch-bar');
                    const {right, top, left} = touchBarEle.getBoundingClientRect();

                    this.tranTop = top - 40;

                    if (this.$i18n.locale === 'ar') {
                        if (left < 200) {
                            this.tranLeft = right + 2;
                            this.tranRight = 'auto';
                        } else {
                            this.tranRight = window.innerWidth - left - sessionWidth - leftMenuWidth + 2;
                            this.tranLeft = 'auto';
                        }
                    } else {
                        if (right + 200 > window.innerWidth) {
                            this.tranLeft = 'auto';
                            this.tranRight = window.innerWidth - left + 2;
                        } else {
                            this.tranLeft = right + 2 - sessionWidth - leftMenuWidth;
                            this.tranRight = 'auto';
                        }
                    }
                } catch (error) {
                    console.log(`🚀 ~ openRightPanel ~ error:`, error);
                }
            }

            const wrapper = document.getElementsByClassName('message-box')[0];
            const wrapperHeight = parseInt(getComputedStyle(wrapper).height);

            // 垂直偏移 + 弹出层高度 > 总容器高度 - 输入框高度
            // 上移弹出层
            // 设置右键菜单列表项
            const progress =
                this.$store.state.fileProcessCollection.fileList[info?.plainMsg?.dialogId]?.[info?.plainMsg?.m?.uuid]?.progress ||
                F.paths(info, 'plainMsg', 'm', 'meta', 'progress');

            let sendStatus = progress?.sendStatus;
            let type = F.paths(info, 'plainMsg', 'm', 'meta', 'type');
            let status = sendStatus || 'default';
            let allRightOptions = [];
            let announcementId = '';
            if (info.chatComponentType === 'MESSAGE')
                announcementId = F.paths(info, 'plainMsg', 'm', 'meta', 'announcementInfo', 'announcementId');

            if (info.isExistReplyList || info.isExistGroupPinList) {
                // 回复列表
                allRightOptions = this.produceReplyListRightOptions(info);
                // 回复列表数据来之服务器，文件状态每次都为空
                if (info?.plainMsg?.MIMETYPE == 'x-filetransfer/octet-stream') {
                    let isFileExist = await this.checkFileisExist(info);
                    if (isFileExist) {
                        status = 'downloaded';
                    }
                }
            } else {
                allRightOptions = this.produceOptions(info);
            }
            // 文件上传 消息部分失败，需要修改右边菜单
            if (info.chatComponentType == 'DOCUMENT' && status == 'uploaded' && info.isFail) {
                status = 'uploadPause';
            }

            let options = status && F.paths(allRightOptions, announcementId ? 'AnnouncementCommit' : info.chatComponentType, status);

            if (
                info.chatComponentType === 'PICTURE' &&
                status === 'default' &&
                F.paths(info, 'plainMsg', 'm', 'meta', 'progress', 'type') === 'download'
            ) {
                // 图片default状态，区分上传还是下载，下载得话没有 cancel 菜单项
                options = options.filter(item => item.type !== 'cancel');
            }
            // 跨行菜单仅留复制
            options = F.multiLineMenus(options, info);

            if (options) {
                let temp = options.slice();

                if (type === 'meeting-assistant-msg-del') {
                    // 只保留删除选项
                    temp.shift();
                    temp.shift();
                }

                this.rightPanelOptions = this.filterRightOptions(temp, info, source);
            } else if (info.chatComponentType === 'UNKNOW') {
                this.rightPanelOptions = this.filterRightOptions(allRightOptions[info.chatComponentType], info, source);
            } else {
                let temp = allRightOptions['DEFAULT'].slice();
                this.rightPanelOptions = this.filterRightOptions(temp, info, source);
            }
            if (this.rightPanelOptions && this.rightPanelOptions <= 0) {
                return;
            }

            this.$nextTick(() => {
                const rightOption = this.$refs.rightClickOption;
                const optionHeight = parseInt(getComputedStyle(rightOption.$el).height);
                // console.log('rightOption', rightOption);
                // this.$i18n.locale === 'ar'
                if (this.tranTop + optionHeight > wrapperHeight - 150) {
                    this.tranTop = this.tranTop - optionHeight - 20 + 60;
                }
            });
            // 最后显示选择框，避免瞬间移动

            if (info.uuid === this.currentChatInfo?.uuid) {
                this.setChatContextMenu({visible: true, uuid: info.uuid, eventTrigger: source});
            } else {
                console.error('uuid change for openRightPanel');
            }
        },
        // 点击全局组件，关闭选项菜单
        closeRightPanel() {
            if (this.chatContextMenuVisible) {
                this.setChatContextMenu({visible: false});
            }
        },
        async commentHandler(info, isOpen = true) {
            console.log('onCaptureGlobalClick', info);
            Caught.errorWith(
                async () => {
                    await Peer.checkIfsendMsg(this.actDialogId, defaultSpaceId());
                    this.$refs.chatHeadRef.commentHandler(info, isOpen);
                },
                {loading: 'custom'}
            );
        },
        copyMessageHandler(info) {
            console.log('copyMessageHandler', info);
            if (window.getSelection().toString()) {
                document.execCommand('copy');
                window.getSelection().empty();
            } else {
                let data;
                const restrictedType = F.paths(info, 'plainMsg', 'm', 'meta', 'restrictedType');
                if (info.chatComponentType === 'RESTRICTED' && restrictedType && msgRestrictedTypeInfo[restrictedType]) {
                    const str = msgRestrictedTypeInfo[restrictedType];
                    data = str ? `[${str}]` : data;
                } else if (info.chatComponentType === 'RICHTEXT' || info.chatComponentType === 'Announcement') {
                    data = parseMarkdownToPlainText(info.plainMsg.body);
                } else {
                    data = info.plainMsg.body;
                }

                /* write to the clipboard now */
                navigator.clipboard.writeText(data);
            }
            this.$message.success(this.$t('right_content_panel.copySuccess'));
        },
        async scrollToMetioned() {
            let headLegth = this.metionedList.length;
            let head = this.metionedList[headLegth - 1];
            this.scrollToMetionedId = head;
            this.scrollToView(head);
            // 删除@
            await Peer.saveMentioned(this.$store.state.uiControl.actDialogId, head, defaultSpaceId(), true);
            await this.$store.dispatch('uiControl/refreshMetioned');
            this.receiptTopBtnName = 'scrollToMetioned';
        },
        async metionedOne() {
            let head = this.metionedList[0];
            let element = document.getElementById(head);
            let spaceId = defaultSpaceId();
            if (element == null) {
                // 没有都删除
                await Peer.saveMentioned(this.$store.state.uiControl.actDialogId, head, spaceId, true);
                await this.$store.dispatch('uiControl/refreshMetioned');
            } else {
                // 定位到该位置
                element.tabIndex = 2;
                element.focus();
            }
        },
        async clearChatList() {
            // console.log('fetchMessage 4');
            let list = await this.fetchMessage({forceStime: 0});
            this.viewChatList = list;

            this.$nextTick(() => {
                this.setLimitUserSelectCheckbox('a7');
            });
        },
        async produceFile(fileList, type) {
            const isForceFile = type === 'fileInput';

            fileList = Array.from(fileList);
            if (fileList.length === 0) {
                this.$message(this.$t('im_select_file'));
                return;
            }

            const spaceInfo = this.getSpaceInfo(defaultSpaceId());

            // 同时限制文件和图片上传
            if (this.fileAcceptInfo && this.imageLimitInfo) {
                const spaceName = this.fileAcceptInfo.originName || spaceInfo?.name;
                if (this.getCurrentSpaceType) {
                    this.$message({type: 'warning', message: this.$t('forbidden_send_file', [spaceName])});
                } else {
                    this.$message({type: 'warning', message: this.$t('enterprise_forbidden_send_file', [spaceName])});
                }
                return;
            }

            // 限制图片上传
            if (!isForceFile && this.imageLimitInfo && validationFileCludesImage(fileList)) {
                const spaceName = this.imageLimitInfo.originName || spaceInfo?.name;
                if (this.getCurrentSpaceType) {
                    this.$message({type: 'warning', message: this.$t('forbidden_send_photo', [spaceName])});
                } else {
                    this.$message({type: 'warning', message: this.$t('enterprise_forbidden_send_photo', [spaceName])});
                }
                return;
            }

            // 限制文件上传
            if (this.fileAcceptInfo) {
                // 当前空间是否有限制发送文件
                const isPictureType = isForceFile ? false : await checkImageFileType(fileList);
                // 判断是否为图片，且判断文件后缀名是否符合要求
                const isValid = isForceFile ? false : validationFileExtname(fileList);
                if (!isPictureType || !isValid) {
                    const spaceName = this.fileAcceptInfo.originName || spaceInfo?.name;
                    if (this.getCurrentSpaceType) {
                        this.$message({type: 'warning', message: this.$t('forbidden_send_file', [spaceName])});
                    } else {
                        this.$message({type: 'warning', message: this.$t('enterprise_forbidden_send_file', [spaceName])});
                    }
                    return;
                }
                if (fileList.some(item => item.size > 1024 * 1024 * 10)) {
                    this.$message({type: 'warning', message: this.$t('im_photo_exceeded_the_10M_limit')});
                    return;
                }
            }
            if (fileList && fileList.length > 0) {
                let fileSizeList = [];
                let hasImg, hasFile;

                for (let fileIdx = 0; fileIdx < fileList.length; fileIdx++) {
                    let fileLimit = 1024 * 1024 * 1024 * 1;
                    let imageLimit = 1024 * 1024 * 10;
                    const fileItem = fileList[fileIdx];
                    // 图片为10M limit
                    if (!isForceFile && LIMIT_IMG_TYPE.indexOf(fileItem.typex) >= 0 && fileItem.size < imageLimit) {
                        if (fileItem.size <= 0) {
                            this.$message(this.$t('im_select_small_file'));
                        } else {
                            hasImg = true;
                            fileSizeList.push({
                                size: fileItem.size,
                                file: fileItem
                            });
                        }
                    } else {
                        // 1G limite
                        if (fileItem.size >= fileLimit) {
                            if (fileList.length == 1) {
                                this.$message(this.$t('im_select_single_than_1G_file'));
                            } else {
                                this.$message(this.$t('im_select_than_1G_file'));
                            }
                        } else {
                            if (fileItem.size <= 0) {
                                this.$message(this.$t('im_select_small_file'));
                            } else {
                                hasFile = true;
                                fileSizeList.push({
                                    size: fileItem.size,
                                    file: fileItem
                                });
                            }
                        }
                    }
                }
                if (fileSizeList.length > 50 && hasFile) {
                    this.$message({
                        message: this.$t('im_select_than_50_files'),
                        customClass: 'zZindex'
                    });
                    fileSizeList = fileSizeList.slice(0, 50);
                    hasImg = false;
                    hasFile = false;
                }
                if (fileSizeList.length > 50 && hasImg) {
                    this.$message({
                        message: this.$t('im_select_than_50_images'),
                        customClass: 'zZindex'
                    });
                    fileSizeList = fileSizeList.slice(0, 50);
                    hasImg = false;
                    hasFile = false;
                }
                if (fileSizeList.length > 0) {
                    if (this.notOrientation) {
                        await this.$store.dispatch('fileCollection/selectFile', {
                            fileList: fileSizeList,
                            isForceFile
                        });
                        devFileLog.info(
                            '[produceFile]',
                            'produceFile after',
                            fileSizeList.map(f => f.file.name)
                        );
                    } else {
                        let rotatePathList = await this.handleOrientation(fileSizeList);
                        let fileResList = await this.formateFileList(fileSizeList, rotatePathList);
                        await this.$store.dispatch('fileCollection/selectFile', {
                            fileList: fileResList,
                            isForceFile
                        });

                        devFileLog.info(
                            '[produceFile][Orientation]',
                            'produceFile after',
                            fileResList.map(f => f.file.name)
                        );
                    }

                    this.$store.state.uiControl.sendDialogVisble = true;
                    // console.timeEnd('PASTE_CAPTURE_IMG')

                    this.sendDialogDisplay = true;
                }
            } else {
                this.$message(this.$t('im_select_file'));
            }
        },

        async formateFileList(originFileList, filePathMapList) {
            return originFileList.reduce((sum, fileItem, fileIdx) => {
                let curPathItem = filePathMapList.find(fileItem => {
                    return fileItem.index == fileIdx;
                });
                sum.push({
                    size: fileItem.size,
                    file: {
                        name: fileItem.file.name.replace(/#|%/g, '-'),
                        path: curPathItem.path,
                        size: fileItem.file.size,
                        type: fileItem.file.type,
                        isReplaceIgnoreSymbol: curPathItem.isReplaceIgnoreSymbol
                    }
                });
                return sum;
            }, []);
        },
        handleOrientation(fileSizeList) {
            let imgPathList = [];
            let otherPathList = [];
            for (let index = 0; index < fileSizeList.length; index++) {
                const cFile = fileSizeList[index];
                if (LIMIT_IMG_TYPE.includes(cFile.file.type)) {
                    imgPathList.push({
                        index,
                        path: cFile.file.path
                    });
                } else {
                    otherPathList.push({
                        index,
                        path: cFile.file.path
                    });
                }
            }
            ipcRenderer.removeAllListeners('rotate-img');
            return new Promise((res, rej) => {
                ipcRenderer.removeAllListeners('rotate-img-Res');
                ipcRenderer.on('rotate-img-Res', (ipc, imgResList) => {
                    res([...otherPathList, ...imgResList]);
                });
                ipcRenderer.send('rotate-img', imgPathList);
            });
        },
        fileChangeHandle_(e, type) {
            Caught.errorWith(async () => {
                let current = this.$store.state.uiControl.actDialogId;
                await Peer.checkIfsendMsg(current);
                this.fileChangeHandle(type);
            });
        },
        async fileChangeHandle(type) {
            let _this = this.$refs[type];
            let fileList = _this.files;
            this.produceFile([...fileList], type);
            // 文件取消
            _this.value = null;
        },
        async dropFileHandle(fileList) {
            this.produceFile(fileList);
        },
        emitSendMsg(triggerDom) {
            triggerDom.emitMsg();
        },
        // 比较输入框@成员与 sendHidArr 的成员一致性
        compareRemind(sendHidArr, text) {
            sendHidArr = sendHidArr.slice();
            let arr = [];
            let nameArr = text.split(/[@|\s]/g).map(ele => {
                ele = ele.replace(/\s/g, '');
                return ele;
            });

            for (var i = 0; i < nameArr.length; i++) {
                if (nameArr[i] === '') {
                    nameArr.splice(i, 1);
                }
            }

            for (let name of nameArr) {
                for (let ele of sendHidArr) {
                    if (name.indexOf(ele.name) !== -1) {
                        arr.push({
                            hid: ele.hid,
                            len: ele.name.length + 1,
                            start: text.indexOf(ele.name)
                        });
                    }
                }
            }
            return arr;
        },
        async sendLongMsg(text) {
            Caught.errorWith(
                async () => {
                    let current = this.$store.state.uiControl.actDialogId;
                    await Peer.checkIfsendMsg(current);
                    this.sendMsgHandle(text, true);
                },
                {loading: 'custom'}
            );
        },
        async sendMsgWithVerif(text) {
            Caught.errorWith(
                async () => {
                    let current = this.$store.state.uiControl.actDialogId;
                    await Peer.checkIfsendMsg(current);
                    this.sendMsgHandle(text);
                },
                {loading: 'custom'}
            );
        },
        async sendMsgHandle(text, isLongText = false, isEditMessage = false) {
            if (!text) {
                return;
            }
            if (process.env.npm_lifecycle_event === 'electron:test' || process.env.NODE_ENV === 'development') {
                if (regDebugMessage(text)) {
                    batchSendMsgHandle(text, this.actDialogId, defaultSpaceId());
                    return;
                }
            }

            F.draft(this.actDialogId + defaultSpaceId(), {});
            let oldObj = this.$store.state.sessionCollection[enCodeSpaceHid(this.$store.state.uiControl.actDialogId, defaultSpaceId())];
            if (oldObj) {
                oldObj.draftObj = {};
                this.$store.dispatch('sessionCollection/changeSession', {
                    session: oldObj,
                    spaceId: defaultSpaceId()
                });
            }

            // console.log('draft== sendMsgHandle', this.actDialogId + defaultSpaceId(), '');
            this.$store.commit('uiControl/currentDialogBottom', true);
            // @消息
            let sendHidArr = this.$store.state.uiControl.sendHidArr;
            let parseResultPlain = encodeForMetion(text);
            let parseResult = parseResultPlain;
            parseResult.ref = parseResultPlain.ref.map(a => {
                a.hid = a.hid == 'all' ? forceAddEqForHid(this.actDialogId) : forceAddEqForHid(a.hid);
                return a;
            });
            // let refList = this.compareRemind(sendHidArr,text) || [];
            let sendTo = this.$store.state.uiControl.actDialogId;

            let msgTemplate = new MsgTemplate();
            let msg = msgTemplate.getMsgTemplate(sendTo, parseResult.body, parseResult.ref);

            if (isEditMessage) {
                msg.m.MIMETYPE = 'edit/text/plain';

                if (!msg.m.meta) {
                    msg.m.meta = {};
                }

                msg.m.meta.editInfo = {
                    uuidEdited: this.editMessageState.uuid,
                    editedMsgStime: this.editMessageState.stime
                };

                this.editMessageState.uuid = '';
            }

            if (isLongText) msg.m.MIMETYPE = 'longtext/plain';
            if (F.paths(this.currentDialogReplyInfo, 'replyPannelVisible')) {
                let replyChatInfo = this.currentDialogReplyInfo.replyChatInfo;
                msg.m.meta = msg.m.meta || {};
                let repliedInfo = F.paths(replyChatInfo, 'plainMsg', 'meta', 'repliedInfo') || {};
                msg.m.meta.repliedInfo = {
                    uuidReplied: replyChatInfo.uuid,
                    uuidRepliedRoot: repliedInfo.uuidRepliedRoot || replyChatInfo.uuid,
                    mimeReplied: F.paths(replyChatInfo, 'plainMsg', 'MIMETYPE'),
                    senderUidReplied: dataUtil.hidToUid(replyChatInfo.peerId),
                    previewReplied: msgTemplate.setPreviewRepliedTemplate(replyChatInfo)
                };
                this.clearReplyPannel();
            }
            let parm = msgTemplate.setList(msg);
            parm.fromLocal = true;
            console.log('isBeBlockInE2EE', parm);

            // 关闭文件发送弹框
            this.sendDialogDisplay = false;
            // 清空发送 @群成员 信息集合
            if (sendHidArr.length > 0) this.$store.commit('uiControl/setSendHidArr', []);
            // 仿照新消息，接受消息
            await handleMessage(parm);

            let needE2EMessage = _.merge({}, parm);

            // 发送消息
            this.$emit('sendMsgFromPannel', {
                payload: needE2EMessage,
                cmd: 'HyperText'
            });
            // 发消息时 清除@消息 里面有判断没有不做清除操作数据库
            // this.$store.commit('sessionCollection/remindChange', {
            //     peerHid: this.$store.state.uiControl.actDialogId,
            //     spaceId
            // });
            this.$store.commit('uiControl/setMetioned', []);

            // 左边会话列表滚动到顶部
            const dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
            const st = setTimeout(() => {
                window.clearTimeout(st);
                if (dom) {
                    dom.scrollTop = 0;
                }
            }, 0);
        },
        // 发送会议卡片回复消息
        async sendMeetingCardReplyMsg(text, sendTo, meetingInfo) {
            F.draft(sendTo + defaultSpaceId(), {});
            let oldObj = this.$store.state.sessionCollection[enCodeSpaceHid(sendTo, defaultSpaceId())];
            if (oldObj) {
                oldObj.draftObj = {};
                this.$store.dispatch('sessionCollection/changeSession', {
                    session: oldObj,
                    spaceId: defaultSpaceId()
                });
            }
            // console.log('draft== sendMeetingCardReplyMsg', sendTo + defaultSpaceId(), '');

            let msgTemplate = new MsgTemplate();
            let msg = msgTemplate.getMeetingCardReplyTemplate(sendTo, text, meetingInfo);

            let parm = msgTemplate.setList(msg);
            parm.fromLocal = true;

            // 仿照新消息，接受消息
            await handleMessage(parm);

            let needE2EMessage = _.merge({}, parm);
            // 发送消息
            this.$emit('sendMsgFromPannel', {
                payload: needE2EMessage,
                cmd: 'HyperText'
            });

            // 左边会话列表滚动到顶部
            const dom = document.querySelector('.sessionlist-box > .el-scrollbar__wrap');
            const st = setTimeout(() => {
                window.clearTimeout(st);
                if (dom) {
                    dom.scrollTop = 0;
                }
            }, 0);
        },
        // 定位未读消息或者@消息渲染区间,区间大小为pageSize 以及上拉加载方法
        async renderAreaAndUpLoad(index) {
            let CurUuidList = filterText(this.dbList);
            let flag = false;
            let end = '';
            let disList = '';
            this.isPosition = true;
            this.viewChatList = [];
            this.curPage = Math.ceil(index / this.pageSize);

            // uuid 集合小于pageSize
            if (CurUuidList.length > 0 && CurUuidList.length <= this.pageSize) {
                this.positionIndex = CurUuidList.length - index;
                var start = -(this.pageSize * this.curPage);
                disList = CurUuidList.slice(start);
            } else {
                // 计算 定位 灰色背景下标和 截取区段值
                let alternateNum = this.pageSize - (index % this.pageSize);
                let posIndex = CurUuidList.length - index;

                if (alternateNum > posIndex) {
                    this.positionIndex = posIndex;
                    // let start = 0;
                    end = this.pageSize + (index % this.pageSize);

                    // 解决重复消息问题
                    if (index % this.pageSize !== 0) {
                        this.curPage = this.curPage - 1;
                        flag = true;
                    }
                } else {
                    this.positionIndex = alternateNum === this.pageSize ? 0 : alternateNum;
                    end = -((this.curPage - 1) * this.pageSize);
                }

                if (this.curPage === 1) {
                    disList = CurUuidList.slice(start);
                } else {
                    disList = CurUuidList.slice(start, end);
                }
            }
            await this.getChatList(disList, false);

            // 赋值 滚动底部条件触发加载 页码
            this.curPageBottom = this.curPage - 1;

            if (flag) {
                this.curPage = this.curPage + 2;
            } else {
                this.curPage++;
            }
            this.isPosition = false;
            this.clickLoading = false;
        },
        // 判断是否加载到底部 （最新消息渲染出来了）
        isNewMessage() {
            let dbList = filterText(this.dbList);
            let lastUuid = dbList.slice(-1)[0];
            let lastMsgUuid = this.viewChatList.slice(-1)[0].uuid;

            if (lastUuid === lastMsgUuid) {
                return true;
            } else {
                return false;
            }
        },
        async viewTipHanle() {
            this.clickLoading = true;
            switch (this.floatTipType) {
                case 'AT':
                    // 点击计算当前 @消息位置
                    this.computedIndex();
                    await this.renderAreaAndUpLoad(this.remindIndex);
                    // 执行定位引导用户
                    this.$nextTick(() => {
                        this.messagePosition(this.positionIndex);
                    });
                    break;
                case 'NEW':
                    if (this.downUnReadCount > 0 && !this.isShowUp) {
                        let isNewMessage = this.isNewMessage();
                        if (isNewMessage) {
                            this.scrollBottom();
                            this.clickLoading = false;
                        } else {
                            // 分区段加载到底部
                            await this.renderAreaAndUpLoad(1);
                            // 执行定位引导用户
                            this.$nextTick(() => {
                                this.messagePosition(this.positionIndex);
                            });
                        }
                    } else if (this.isShowUp) {
                        // 计算区间，未读消息区间
                        await this.renderAreaAndUpLoad(this.upUnreadCount + this.downUnReadCount);
                        // 执行定位引导用户
                        this.$nextTick(() => {
                            this.messagePosition(this.positionIndex);
                        });
                    }
                    break;
                default:
                    break;
            }
        },
        // 按需获取消息，并展示。
        // isInstead : 在当前会话窗体撤回消息是为true，需要替换整个展示消息列表，才能渲染撤回消息。
        async getChatList(tempDBList, isUpLoad, isInstead = false, isForceInstead = false) {
            let preItem = '';
            let resultList = [];
            let spaceId = defaultSpaceId();
            for (let dbIdx = 0; dbIdx < tempDBList.length; dbIdx++) {
                const val = tempDBList[dbIdx];
                let msgFromStorage = await getMessageWithDatabase(val, spaceId);
                msgFromStorage = msgFromStorage.length > 0 ? msgFromStorage[0] : '';
                if (msgFromStorage.isDeleted) {
                    continue;
                }
                if (msgFromStorage) {
                    if (!dataUtil.isNotificationMsg(msgFromStorage.m.MIMETYPE || msgFromStorage.c)) {
                        if (preItem) {
                            let preMsgFromstorage = await getMessageWithDatabase(preItem, spaceId);
                            preMsgFromstorage = preMsgFromstorage.length > 0 ? preMsgFromstorage[0] : '';
                            if (preMsgFromstorage.peerId != msgFromStorage.peerId) {
                                msgFromStorage.isShowTimeStamp = true;
                            } else {
                                if (msgFromStorage.m.stime >= preMsgFromstorage.m.stime + CHAT_TIME_INTERVAL) {
                                    msgFromStorage.isShowTimeStamp = true;
                                } else {
                                    msgFromStorage.isShowTimeStamp = false;
                                }
                            }
                        } else {
                            msgFromStorage.isShowTimeStamp = true;
                        }
                        preItem = val;
                        resultList.push(renderMsgItem(msgFromStorage, this.$store.state.userInfo.hid));
                    }
                }
            }
            resultList.sort(function (perVal, curVal) {
                return perVal.plainMsg.m.stime - curVal.plainMsg.m.stime;
            });
            // 最后一条为撤回消息时，需替换
            if (isInstead) {
                // 防止消息分批加载重复
                if (this.curPageBottom <= 0) {
                    this.viewChatList = resultList;
                }
            } else {
                if (isUpLoad) {
                    this.viewChatList.unshift(...resultList);
                } else if (isForceInstead) {
                    this.viewChatList = resultList;
                } else {
                    this.viewChatList.push(...resultList);
                }
            }

            this.$nextTick(() => {
                this.computedrRemidFlag();
            });

            this.$nextTick(() => {
                this.setLimitUserSelectCheckbox('a8');
            });
        },

        fetchRegionMessage(currentReadToken, spaceId, region, dir) {
            const regionCallback = async (uuidList, nextRegion) => {
                // newMsgQueue.addTask(this.regionCallback.bind(this), uuidList, region, dir);
                if (nextRegion && currentReadToken === this.currentReadToken) {
                    this.fetchRegionMessage(currentReadToken, spaceId, nextRegion, dir);
                }
            };
            fetchOfflineMessageHighPriorityTask(fetchCurrentDialogOfflineMsg, currentReadToken, spaceId, region, dir, regionCallback);
        },

        async fetchMessage({forceStime, forceMsgSeq}, dir = 'after', msguuid, uuids, needRegion = true) {
            const fetchMessageKey = this.fetchMessageKey;
            const currentReadToken = this.currentReadToken;
            let stime = 0,
                msgSeq;
            let renderMessage, seqMessage;
            if (dir == 'after') {
                renderMessage = this.viewChatList[0] || {stime: 0};
                seqMessage = this.viewChatList.find(item => item.msgSeq);
            } else {
                renderMessage = this.viewChatList[this.viewChatList.length - 1] || {stime: 0};
                seqMessage = this.viewChatList.findLast(item => item.msgSeq);
            }
            if (forceStime !== undefined) {
                stime = forceStime;
            } else {
                stime = renderMessage.stime;
            }

            if (forceMsgSeq !== undefined) {
                msgSeq = forceMsgSeq;
            } else {
                msgSeq = seqMessage?.msgSeq;
            }
            console.log(
                '🚀 ~ file: RightContentPanel.vue ~ line 3357 ~ fetchMessage ~ stime, currentReadToken, dir',
                stime,
                msgSeq,
                currentReadToken,
                dir
            );
            let messages = [];

            if (uuids) {
                messages = await Peer.fetchMessageFromUUIDList(currentReadToken, uuids);
            } else if (msguuid) {
                let msg = await Peer.fetchMessageByUUID(msguuid);
                if (msg) {
                    messages.push(msg);
                } else {
                    messages = await Peer.fetchMessageFrom({stime, msgSeq}, currentReadToken, dir);
                }
            } else {
                messages = await Peer.fetchMessageFrom({stime, msgSeq}, currentReadToken, dir);

                if (needRegion && appdataStorage.getItem('imSdkOpen')) {
                    const historyMsgMaxSeq = this.mySession?.historyMsgMaxSeq || -1;

                    const cloneMessage = _.cloneDeepWith(messages)
                        .filter(item => {
                            return item.msgSeq && item.msgSeq >= historyMsgMaxSeq;
                        })
                        .sort((a, b) => a.msgSeq - b.msgSeq);

                    const regionData = getRegions(cloneMessage);
                    console.warn(`🚀 ~ fetchMessageFrom ~ regionData:`, regionData);
                    msgDevLog.log('fetchMessageFrom -> regionData', currentReadToken, regionData);
                    for (let prop in regionData) {
                        this.fetchRegionMessage(currentReadToken, defaultSpaceId(), regionData[prop], dir);
                    }
                }
            }

            console.log('获取消息结束1---', arguments, _.cloneDeepWith(messages));

            // FIX: Compatible with old versions, handle re-edit messages.
            const editMessageList = messages.filter(
                item => item?.MIMETYPE === 'edit/text/plain' || item?.MIMETYPE === 'edit/richtext/plain'
            );
            messages = messages.filter(item => item.MIMETYPE !== 'edit/text/plain' && item.MIMETYPE !== 'edit/richtext/plain');

            editMessageList.forEach(async editMessage => {
                const spaceId = getSpaceId(editMessage.mcTo);

                const decodeEditMessage = decodeMessageTransform(_.cloneDeep(editMessage));

                const index = messages && messages.findIndex(item => item.uuid === decodeEditMessage?.m?.meta?.editInfo?.uuidEdited);
                if (index >= 0) {
                    const decodeMessage = decodeMessageTransform(_.cloneDeep(messages[index]));
                    messages[index] = replaceMessageByEditText(decodeMessage, decodeEditMessage);
                }

                const newMessage = await editMessageHandle(decodeEditMessage, spaceId);

                await this.$store.dispatch('messageCollection/saveMessage', {
                    message: newMessage
                });

                deleteMessage(editMessage.uuid, spaceId);
            });

            let allList = messages.reduceRight((result, msg) => {
                msg.isShowTimeStamp = true;
                msg.isShowDate = true;
                msg = decodeMessageTransform(msg);
                let last = result.length != 0 ? result[result.length - 1] : dir == 'after' ? {stime: 0} : renderMessage;
                msg = renderMsgItem(msg);

                // console.log('last---', _.cloneDeepWith(last));
                // console.log('msg---', _.cloneDeepWith(msg));

                F.timeLogic(msg, last);
                F.dateLogic(msg, last);
                result.push(msg);

                return result;
            }, []);

            // console.log('获取消息结束2---', _.cloneDeepWith(allList));

            if (dir == 'after') {
                F.dateLogic(renderMessage, allList[allList.length - 1] || {stime: 0});
                F.timeLogic(renderMessage, allList[allList.length - 1] || {stime: 0});
            }
            allList = await this.handleReplyMessage(allList);

            // * fix: 如果读取数据库过程中用户切换会话，会导致取到的数据和期望不一致
            if (fetchMessageKey !== this.fetchMessageKey) {
                msgDevLog.log('[fetch key change]', currentReadToken, this.currentReadToken, fetchMessageKey, this.fetchMessageKey);
                console.error('[fetch key change]', currentReadToken, this.currentReadToken, fetchMessageKey, this.fetchMessageKey);
                return Promise.reject('fetch key change');
            }

            const res = allList.map(a => {
                if (a.uuid == this.newMessagesUUID) {
                    a.isShowTimeStamp = true;
                    a.unreadMessage = true;
                } else {
                    a.unreadMessage = false;
                }
                return a;
            });
            console.log('fetchMessage res', _.cloneDeepWith(res));

            allList = null;
            renderMessage = null;
            messages = null;

            return res;
        },
        async formateMessageDbToView(messageList) {},
        //处理消息回复逻辑
        async handleReplyMessage(chatList) {
            let repliedList = [];
            let messageMap = {};
            chatList.forEach(msg => {
                let uuidReplied = F.paths(msg, 'plainMsg', 'meta', 'repliedInfo', 'uuidReplied');
                if (uuidReplied) {
                    msg.uuidReplied = uuidReplied;
                    repliedList.push(uuidReplied);
                }
                messageMap[msg.uuid] = msg;
            });
            let repliedResult = {};
            let requestDbList = [];
            repliedList.forEach(uuid => {
                if (messageMap[uuid]) {
                    repliedResult[uuid] = messageMap[uuid];
                } else {
                    requestDbList.push(uuid);
                }
            });
            if (requestDbList.length) {
                let res = await getMessageWithDatabase(requestDbList, defaultSpaceId());
                if (res.length > 0) {
                    res.forEach(item => {
                        repliedResult[item.uuid] = renderMsgItem(item);
                    });
                }

                res = null;
            }

            const res = chatList.map(msg => {
                if (msg.uuidReplied) {
                    let repliedInfo = repliedResult[msg.uuidReplied];
                    if (!repliedInfo) {
                        let msgTemplate = new MsgTemplate();
                        repliedInfo = msgTemplate.getPreviewRepliedTemplate(F.paths(msg, 'plainMsg', 'meta', 'repliedInfo'));
                        repliedInfo.uuid = msg.uuidReplied; //用来接收回复的图片加载完成用的
                        msgTemplate = null;
                    }
                    msg.repliedInfo = repliedInfo;

                    repliedInfo = null;
                }
                return msg;
            });

            chatList = null;
            repliedList = null;
            messageMap = null;
            repliedResult = null;
            requestDbList = null;

            return res;
        },
        scrollToUUID(uuid) {
            let ele = document.getElementById(uuid);
            if (ele) {
                ele.tabIndex = '1';
                ele.focus();
                delete ele.tabIndex;
            }
        },
        scrollToUUIDOnTop(uuid) {
            const ele = document.getElementById(uuid);
            const containerView = this.$refs.container_view?.$el;
            if (ele && containerView) {
                containerView.scrollTop = ~~(ele.offsetTop - containerView.offsetHeight / 2 + 32);
            }
        },
        async scrollTopLoadMore() {
            if (this.dataInited) {
                this.scrollTopLoadMore_();
            }

            // console.log('Load more message will triggered by loadmore-dom');
            // 停止播放
            Bus.$emit('voice-play', '');
        },
        async scrollTopLoadNew_() {
            if (this.isNewestView) {
                void null;
            } else {
                if (stautsOfnewloading) {
                    return;
                }
                stautsOfnewloading = true;
                let last = this.viewChatList[this.viewChatList.length - 1];

                const viewLastSeqMsg = this.viewChatList.findLast(item => item.msgSeq);

                // console.log('fetchMessage 5');
                let newMsgs = await this.fetchMessage({forceStime: last.stime}, 'before');
                if (newMsgs.length == 0 || newMsgs.length < 30) {
                    this.isNewestView = true;
                }
                if (newMsgs.length) {
                    let uuids = this.viewChatList.map(item => item.uuid);
                    newMsgs = newMsgs.filter(item => !uuids.includes(item.uuid));
                    uuids = null;

                    if (newMsgs.length && appdataStorage.getItem('imSdkOpen')) {
                        const newListTopMsg = newMsgs.find(item => item.msgSeq);

                        if (newListTopMsg && viewLastSeqMsg && newListTopMsg.lastSeq !== viewLastSeqMsg.msgSeq) {
                            const region = {
                                key: uuidv4(),
                                msgSeqStart: viewLastSeqMsg.msgSeq,
                                startSeqStime: viewLastSeqMsg.stime,
                                startUUID: viewLastSeqMsg.uuid,
                                msgSeqEnd: newListTopMsg.msgSeq,
                                endSeqStime: newListTopMsg.stime,
                                endUUID: newListTopMsg.uuid
                            };

                            msgDevLog.log('scrollTopLoadNew_ -> regionData', this.currentReadToken, region);
                            this.fetchRegionMessage(this.currentReadToken, defaultSpaceId(), region, 'after');
                        }
                    }
                }
                this.viewChatList.push(...newMsgs);
                this.watchKeyChange();
                this.$nextTick(() => {
                    stautsOfnewloading = false;
                });
                this.$nextTick(() => {
                    this.setLimitUserSelectCheckbox('a9');
                });
            }
        },
        async scrollTopLoadMore_(again, againType) {
            // console.log('-----触犯了几次------');
            if (statusOfLoading) return;
            statusOfLoading = true;

            let renderMsg = this.viewChatList[0];
            if (again !== 'new' && renderMsg == null) {
                statusOfLoading = false;
                return;
            }
            this.renderTimeTip();
            // console.log('fetchMessage 6 again', again);
            if (again !== 'new' && again !== 'again') {
                this.setPullLoading(true);
            }
            let newList = await this.fetchMessage({});
            console.log(`🚀 ~ newList:`, _.cloneDeepWith(newList));

            if (newList.length != 0 || again == 'again') {
                statusOfLoading = false;
            }

            // console.log('newList', newList);
            // #region fix newList 重复 uuid bug
            let uuids = this.viewChatList.map(item => item.uuid);
            newList = newList.filter(item => !uuids.includes(item.uuid));
            uuids = null;
            // #endregion
            if (newList.length) {
                if (appdataStorage.getItem('imSdkOpen')) {
                    const viewTopMsg = this.viewChatList.find(item => item.msgSeq);
                    const newListLastMsg = newList.findLast(item => item.msgSeq);
                    if (newListLastMsg && viewTopMsg && newListLastMsg.msgSeq !== viewTopMsg.lastSeq) {
                        const historyMsgMaxSeq = this.mySession?.historyMsgMaxSeq || -1;

                        const region = {
                            key: uuidv4(),

                            msgSeqStart: newListLastMsg.msgSeq,
                            startSeqStime: newListLastMsg.stime,
                            startUUID: newListLastMsg.uuid,

                            msgSeqEnd: viewTopMsg.msgSeq,
                            endSeqStime: viewTopMsg.stime,
                            endUUID: viewTopMsg.uuid
                        };

                        if (historyMsgMaxSeq > region.msgSeqStart) {
                            region.msgSeqStart = historyMsgMaxSeq + 1;
                        }
                        if (historyMsgMaxSeq > region.msgSeqEnd) {
                            region.msgSeqEnd = historyMsgMaxSeq + 1;
                        }

                        if (region.msgSeqStart < region.msgSeqEnd) {
                            msgDevLog.log('scrollTopLoadMore_ -> regionData', this.currentReadToken, region);
                            this.fetchRegionMessage(this.currentReadToken, defaultSpaceId(), region, 'after');
                        }
                    }
                }

                this.viewChatList.unshift(...newList);
                this.setPullLoading(false);
                window.clearTimeout(this.pullLoadingTimer);
                statusOfLoading = false;
            }
            this.$nextTick(() => {
                if (newList.length == 0) {
                    void null;
                } else if (againType === 'init') {
                    this.scrollBottom();
                } else {
                    this.scrollToUUIDOnTop(renderMsg.uuid);
                }
            });

            if (newList.length == 0 && again != 'again' && again != 'new') {
                // let hid = await getHid();
                window.historyMessage[renderMsg.uuid] = [];
                window.historyMessageIs[renderMsg.uuid] = {};
                window.historyMessage['function' + renderMsg.uuid] = () => {
                    // console.log('-----VVVVVVVVV-------')
                    statusOfLoading = false;
                    // console.log('什么情况', 'function' + renderMsg.uuid);
                    this.scrollTopLoadMore_('again');
                };
                this.sendPullDetail(this.hid, renderMsg);
            }
            this.pullLoadingTimer = window.setTimeout(() => {
                statusOfLoading = false;
                window.clearTimeout(this.pullLoadingTimer);
                this.setPullLoading(false);
            }, 5000);
            this.$nextTick(() => {
                newList = null;
                renderMsg = null;
                this.setLimitUserSelectCheckbox('a10');
                // 主动垃圾回收
                window.clearCacheMemory('scroll');
            });
            return;
        },
        sendPullDetail(hid, renderMsg) {
            if (appdataStorage.getItem('imSdkOpen')) {
                const params = {
                    dialogId: this.currentReadToken,
                    spaceId: defaultSpaceId()
                };

                params.fetchId = renderMsg ? renderMsg.uuid : this.hid;

                if (renderMsg?.plainMsg?.msgSeq && renderMsg?.plainMsg?.msgSeq > 1) {
                    params.pullMsgSeq = renderMsg?.plainMsg?.msgSeq - 1;
                } else if (renderMsg?.stime) {
                    params.stime = renderMsg?.stime;
                }

                fetchDialogHistoryMsgs(params);
                return;
            }
            const pullData = {
                cmd: 'PullDetail',
                extraHeader: {
                    cmd: 'PullDetail',
                    wdid: 'windows',
                    hid: hid
                },
                payloadArray: [
                    {
                        f: hid,
                        mcFrom: `${defaultSpaceId()}#${hidToNumber(hid)}`,
                        m: {
                            tid: this.currentReadToken,
                            sUUID: renderMsg.uuid,
                            reqId: 'pullDetail-iam-2<@>' + renderMsg.uuid,
                            count: 20,
                            ignoreReciept: renderMsg.stime > appdataStorage.getItem('RECEIPT_START_TIME') ? 1 : 0,
                            sTS: renderMsg.stime
                        }
                    }
                ]
            };
            msgDevLog.log('pullDetail::' + this.currentReadToken + '->' + renderMsg.uuid, JSON.stringify(pullData));
            this.$store.state.uiControl.socketInstance._sendMsg(pullData.payloadArray, pullData.cmd).catch(err => {
                console.error('sendPullDetail', err);
                msgDevLog.error('sendPullDetail::', err, this.currentReadToken + '->' + renderMsg.uuid, JSON.stringify(pullData));
            });
        },
        sendPullNewDetail() {
            if (appdataStorage.getItem('imSdkOpen')) {
                window.historyMessage[this.hid] = [];
                window.historyMessageIs[this.hid] = {};
                window.historyMessage['function' + this.hid] = () => {
                    statusOfLoading = false;
                    this.scrollTopLoadMore_('new', 'init');
                };
                fetchDialogHistoryMsgs({
                    dialogId: this.currentReadToken,
                    spaceId: defaultSpaceId(),
                    fetchId: this.hid
                });

                return;
            }

            const hid = this.hid;
            const tid = this.currentReadToken;
            const pullData = {
                cmd: 'PullDetail',
                extraHeader: {
                    cmd: 'PullDetail',
                    wdid: 'windows',
                    hid: hid
                },
                payloadArray: [
                    {
                        f: hid,
                        mcFrom: `${defaultSpaceId()}#${hidToNumber(hid)}`,
                        m: {
                            tid: tid,
                            reqId: 'pullDetail-iam',
                            count: 20,
                            ignoreReciept: getTimestamp() > appdataStorage.getItem('RECEIPT_START_TIME') ? 1 : 0,
                            sTS: getTimestamp()
                        }
                    }
                ]
            };
            msgDevLog.log('sendPullNewDetail::' + tid + '->', JSON.stringify(pullData));
            this.$store.state.uiControl.socketInstance._sendMsg(pullData.payloadArray, pullData.cmd).catch(err => {
                console.error('sendPullNewDetail', err);
                msgDevLog.error('sendPullNewDetail::', err, tid + '->', JSON.stringify(pullData));
            });
        },
        scrollPosition(dom) {
            let H = dom.scrollHeight - (this.preHeight || 0);
            // 滚动到 距离上面 十个的位置
            dom.scrollTo(0, H);
        },
        async scrollBottomLoadMore() {
            console.log('scrollBottomLoadMore', this.dataInited, this.isNewestView);
            msgDevLog.log('scrollBottomLoadMore', this.dataInited, this.isNewestView);
            if (this.isNewestView == false) {
                this.scrollTopLoadNew_();
            }
        },
        // 向上定位最后一条未读消息
        messagePosition(index) {
            if (index <= -1) {
                // 找不到 定位消息 清除状态
                setTimeout(() => {
                    this.clearMessage();
                    this.$store.commit('uiControl/setRemidUuid', {
                        dialogId: this.currentReadToken,
                        isAdd: false
                    });
                    this.$store.commit('uiControl/setCurUuid', {
                        uuid: ''
                    });
                }, 1000);
            }

            index = index === -1 ? 0 : index;
            let ele = containers[index];
            if (!ele) return;
            ele.scrollIntoView(true);
            let eleChild = ele && ele.children[0];

            let temUp = this.upUnreadCount - this.remindIndex;
            // 向上 @消息上面还有普通消息情况
            if (this.isShowUp && this.remindIndex !== -1 && temUp > 0) {
                setTimeout(() => {
                    eleChild.style.background = 'none';
                    this.tmpCount = temUp;
                    this.remindIndex = -1;
                    this.isRemind = false;
                }, 1000);

                // 向下 @消息下面还有普通消息情况
            } else if (this.remindIndex !== -1 && this.downUnReadCount > 1) {
                setTimeout(() => {
                    eleChild.style.background = 'none';
                    this.tmpCount = this.downUnReadCount;
                    this.remindIndex = -1;
                    this.isRemind = false;
                }, 1000);
            } else {
                setTimeout(() => {
                    eleChild.style.background = 'none';
                    if (this.downUnReadCount > 1) {
                        this.tmpCount = 0;
                        this.$store.commit('uiControl/setUnReadCount', {
                            num: 0
                        });
                    } else {
                        this.clearMessage();
                    }
                }, 1000);
            }
        },
        // 计算 @消息位置 index
        computedIndex(flag = true) {
            if (this.curUuid) {
                let list = filterText(this.dbList);
                let index = Array.isArray(list) && list.findIndex(uuid => uuid === this.curUuid);
                if (flag) {
                    this.remindIndex = list.length - index;
                } else {
                    this.remindIndex = index;
                }
                // 通过@消息的 uuid 计算完dom 下标将 store 里面的uuid 清空
                if (index !== -1) {
                    this.$store.commit('uiControl/setRemidUuid', {
                        dialogId: this.currentReadToken,
                        isAdd: false
                    });
                    this.$store.commit('uiControl/setCurUuid', {
                        uuid: ''
                    });
                }
            }
        },
        async scrollToView(uuid, deepin = 0, afterhook = flashingHook) {
            let foundMsg = this.viewChatList.find(a => a.uuid == uuid);
            if (deepin == 2) {
                void null;
            } else if (foundMsg) {
                // 回复列表 首条消息被删除需提示
                // if (foundMsg && foundMsg.plainMsg?.MIMETYPE == 'application/withdraw') {
                //     this.$message.warning('Message not found');
                //     return;
                // }
                this.scrollToUUIDOnTop(uuid);
                afterhook(uuid);
            } else {
                let msg = await Peer.fetchMessageByUUID(uuid);
                if (msg) {
                    // console.log('fetchMessage 7');
                    // find pin message replace list to pin message  isNewestView is false
                    this.viewChatList = await this.fetchMessage({forceStime: msg.stime - 1}, 'before');
                    this.isNewestView = false;
                    this.$nextTick(() => {
                        this.scrollToView(uuid, deepin + 1);
                    });
                    this.$nextTick(() => {
                        this.setLimitUserSelectCheckbox('a11');
                    });
                } else {
                    if (getTimestamp() - this.prevTipTime >= 300) {
                        this.$message.warning(this.$t('messageNotFound'));
                        this.prevTipTime = getTimestamp();
                    }
                }
            }
        },
        scrollNewMessage(a, deep = 0) {
            this.scrollToView(this.newMessagesUUID);
            this.scrollNewMessageId = this.newMessagesUUID;
            this.receiptTopBtnName = 'scrollNewMessage';
            return;
        },
        async scrollBottomNew() {
            this.scrollBottomNewId = this.viewChatList[0].uuid;
            this.receiptTopBtnName = 'scrollBottomNew';
            // console.log('IntersectionObserver scrollBottomNew');
            if (this.isNewestView) {
                this.scrollBottom();
                return;
            }
            // console.log('fetchMessage 8');
            this.viewChatList = await this.fetchMessage({forceStime: 0});
            this.isNewestView = true;
            this.$store.commit('uiControl/setCurDialogAcount', {
                num: 0
            });
            this.$nextTick(() => {
                this.scrollBottom();
            });
            this.$nextTick(() => {
                this.setLimitUserSelectCheckbox('a12');
            });
        },
        // 滚动条滚到底部
        scrollBottom() {
            // let bottomdiv = document.getElementById("bottomdiv")
            // if(bottomdiv){
            //   bottomdiv.scrollIntoView(true)
            // }
            // return;
            setTimeout(() => {
                // 解决滚动条偶尔不到底部问题
                let el = F.paths(this, '$refs', 'container_view', '$el');
                console.log(`🚀 ~ setTimeout ~ el:`, el.scrollHeight);

                setTimeout(() => {
                    console.log(`🚀 ~ setTimeout ~ el:`, el.scrollHeight);
                }, 3000);

                if (!el) return;
                let H = el.scrollHeight + 100;
                // 滚动条到达底部
                el.scrollTo(0, H);

                this.renderTimeTip();
            }, 0);
            if (this.messageCover) {
                setTimeout(() => {
                    this.messageCover = false;
                });
            }
            this.$store.commit('uiControl/setCurDialogAcount', {
                num: 0
            });
        },
        // 接消息前判断 滚动条是否到达底部
        preMsgAsser() {
            let isUpLoad = true;

            if (!this.firstFlag) {
                this.firstFlag = true;
            } else {
                if (isScrollBarToBottom(this.$refs.container_view.$el)) {
                    // 滚动条到底不了
                    isUpLoad = true;
                } else {
                    isUpLoad = false;
                }
            }

            return isUpLoad;
        },
        // 计算 当前 @消息字眼 是否显示
        computedrRemidFlag() {
            let curUuid = this.$store.state.uiControl.curUuid;
            // 点击 非当前 uuid
            let uuid = this.$store.state.uiControl.remidUuid[this.currentReadToken];
            let _uuid = curUuid || uuid;
            // 设置最早的一次uuid @消息
            this.curUuid = _uuid;
            if (curUuid) {
                this.isCurUuid = true;
            }
            if (_uuid) {
                this.isRemind = true;
            } else {
                this.isRemind = false;
            }
        },
        // 清除 上一个当前会话 @消息关联
        clearRemid(val, oldVal) {
            if (val === oldVal) return;

            // 点击 会话时清空上一次当前 会话的 @消息uuid
            this.$store.commit('uiControl/setRemidUuid', {
                dialogId: oldVal,
                isAdd: false
            });
            this.$store.commit('uiControl/setCurUuid', {
                uuid: ''
            });
        },
        noReceipt(val) {
            return !val.match(/\|/g) || (val.match(/\|/g) && val.match(/\|/g).length < 2);
        },
        addDateUIParm(list) {
            for (let index = 0; index < list.length; index++) {
                const curItem = list[index];
                const preItem = list[index - 1];
                curItem.isShowDate = false;
                if (!preItem) {
                    curItem.isShowDate = true;
                } else {
                    if (new Date(Number(curItem.timestamp)).getDate() - new Date(Number(preItem.timestamp)).getDate() >= 1) {
                        curItem.isShowDate = true;
                    } else {
                        curItem.isShowDate = false;
                    }
                }
            }
            return list;
        },
        // 去重消息列表
        clearmessage(list) {
            if (!list || (list.length && list.length < 1)) {
                return [];
            }
            let obj = {};
            let aimsarray = list.concat();
            let arr = aimsarray.reduce(function (item, next) {
                obj[next.uuid] ? '' : (obj[next.uuid] = true && item.push(next));
                return item;
            }, []);
            arr = this.addDateUIParm(arr);

            return arr;
        },

        clearMessage() {
            this.remindIndex = -1;
            this.isRemind = false;
            this.tmpCount = 0;
            // 当前会话 滚动条滚动到底部 有人@自己 点击标签 清除ref 为空数组
            // this.$store.commit('sessionCollection/remindChange', {
            //     peerHid: this.currentReadToken,
            //     spaceId: defaultSpaceId()
            // });
            this.$store.commit('uiControl/setMetioned', []);
            this.$store.commit('uiControl/setCurDialogAcount', {
                num: 0
            });
            this.$store.commit('uiControl/setUnReadCount', {
                num: 0
            });
        },
        userinfo(item) {
            if (item) {
                return {
                    displayName: item.displayName || item.name,
                    token: item.hid
                };
            } else {
                return {};
            }
        },
        setTimestampMap(timeStamp) {
            if (!this.lastMsgTimestampMap[this.$store.state.uiControl.actDialogId]) {
                this.$set(this.lastMsgTimestampMap, this.$store.state.uiControl.actDialogId, timeStamp);
                return timeStamp;
            } else {
                let preTime = this.lastMsgTimestampMap[this.$store.state.uiControl.actDialogId];
                this.$set(this.lastMsgTimestampMap, this.$store.state.uiControl.actDialogId, timeStamp);
                return preTime;
            }
        },
        // 获取当前群备注
        nickName(item) {
            if (item) {
                if (this.groupInfo && this.groupInfo['name'] && Object.keys(this.groupInfo.name).length > 0) {
                    item = Object.assign({}, item);
                }
            }
            return dataUtil.renderDisplayName(item);
        },
        startScreenCapture(event) {
            clearTimeout(this.screenCaptureTimerId);
            const popoverScreenCapture = Array.from(document.querySelectorAll('.popover-screen-capture'));
            const btnSreenCapture = document.querySelector('.screen-capture');
            if (popoverScreenCapture.length > 0 && btnSreenCapture) {
                popoverScreenCapture.forEach(item => (item.style.display = 'none'));
                btnSreenCapture.style.pointerEvents = 'none';
                this.screenCaptureTimerId = setTimeout(() => (btnSreenCapture.style.pointerEvents = 'unset'), 300);
            }
            this.$nextTick(() => {
                // console.time('capture-screen000');
                ipcRenderer.send('capture-screen', {type: 'start', isHideMainWindow: this.getHideMainWindow()});
            });
        },
        fileClickHandle() {
            // console.log('file click handle');
        },
        onMessageInputFocus() {
            try {
                const messageInputer = this.$refs.messageInputer;
                messageInputer && this.messageInputFocus(messageInputer);
                const messageHiddenInput = this.$refs['message-hidden-input'];
                messageHiddenInput && messageHiddenInput.focus && messageHiddenInput.focus();
            } catch (error) {
                console.error('[error]: ', error);
            }
        },
        messageInputFocus(vElm) {
            vElm.$refs && vElm.$refs.contentEditor.focus();
        },
        cleanAndFocusContentEditor(value, focused) {
            if (value === undefined) {
                return;
            }
            // 都为空字符时，清空并focus
            if (value.match(/^\s*$/) && this.richTextareaEl.html().length > 0) {
                this.richTextareaEl.html('');
                this.lastLength = 0;
                this.wasEmpty = true;

                if (focused) {
                    var self = this;
                    self.focus();
                }
            }
        },
        inputFocusHandle(e) {
            this.isFocus = true;
        },
        inputBlurHandle(e) {
            this.isFocus = false;
            this.$store.commit('uiControl/SET_SUGGESTION_PANNEL_VISABLE', false);
        },
        emojiMouseEnterHandle(ele) {
            this.$refs.messageInputer.emojiClick(ele);
        },
        isInTenMins(preItem, nextItem) {
            if (preItem && nextItem.chatItem.stime - preItem.chatItem.stime >= 60 * 1000) {
                return true;
            }
            return false;
        },
        isSameMessage(preItem, nextItem) {
            if (preItem && preItem.chatItem.uuid === nextItem.chatItem.uuid) {
                return true;
            }
            return false;
        },
        chatTypeComputed(chatItem, isSubscribe) {
            let componentName = CHAT_TYPE[chatItem.chatComponentType];
            return {
                is: componentName,
                isSubscribe,
                chatInfo: chatItem
            };
        },
        async voiceNext(uuid) {
            let nextItem = '';
            for (let idx = 0; idx < this.viewChatListComputed.length; idx++) {
                const msgItem = this.viewChatListComputed[idx];
                if (msgItem.uuid == uuid) {
                    nextItem = this.viewChatListComputed[idx + 1];
                }
            }

            let nextUUID = nextItem?.uuid;
            if (nextUUID) {
                let dbItem = await getDBVoiceIsListened({
                    uuid: nextUUID,
                    spaceId: getSpaceId(nextItem?.plainMsg?.mcTo)
                });
                let isListened = dbItem?.isListened ? true : false;
                if (this.$refs[nextUUID] && typeof this.$refs[nextUUID].play == 'function' && !isListened) {
                    this.$refs[nextUUID].play();
                }
            }
        },
        ifLoadMoreIsNeeded(direct) {
            if (direct) {
                if (direct === 'up') {
                    console.log('scrolltop');
                } else if (direct === 'down') {
                    console.log('scrollbottom');
                } else {
                    // No more message to be loaded
                }
            } else {
                console.error('[error]: ', 'no topdiv in doms');
            }
        },
        scrollHandle: _.throttle(async function (dom, scrollTop, direct) {
            console.log('scrollHandle00', scrollTop, direct);
            this.renderTimeTip();
            // if (this.dataInited) this.ifLoadMoreIsNeeded(direct);
            if (dom.scrollHeight - (scrollTop + dom.offsetHeight) > 30) {
                this.$store.commit('uiControl/currentDialogBottom', false);
                console.log('totop');
            } else {
                console.log('tobottom');
                this.$store.commit('uiControl/currentDialogBottom', true);
                this.readStatusMultiSync();
            }
            readMessageMetioned(dom, this);
        }, 1500),
        addEventlistener(selector, handle, fn) {
            document.querySelector(selector).addEventListener(handle, fn.bind(this));
        },
        removeEventListener(selector, handle, fn) {
            document.querySelector(selector).removeEventListener(handle, fn.bind(this));
        },
        clipboardData(data) {
            if (data.files?.length) {
                this.produceFile(data.files);
            }
        },
        async copyFile() {
            let filePath = this.currentChatInfo.plainMsg.assertPath;
            if (filePath) {
                await setClipboard('img', filePath);
                this.$message.success(this.$t('right_content_panel.copySuccess'));
            }
        },
        onCardClose(e) {
            this.contactCard.visible = false;
            if (this.recipientCardObj.showValue) {
                // 判断鼠标是否在回复列表区域内
                const position = this.$refs?.RecipientCard?.getPopoutClientRect();
                if (
                    !(
                        position &&
                        e.clientX >= position.left &&
                        e.clientX <= position.right &&
                        (e.clientY >= position.top) & (e.clientY <= position.bottom)
                    )
                ) {
                    this.recipientCardObj.showValue = false;
                }
            }
        },
        onDialogRightClick() {},
        // source:  fileDialogDownload 是代表从seeting -> files中下载文件
        async handleCurrentMsgList(msg, source) {
            // let atomRun = F.globalAtomRun;
            // 上传时取消，需删除该消息
            msgDevLog.log('newMsgComing uuid', msg.m?.uuid, source);

            //删除所有消息
            if (msg.clearAll) {
                this.newMessagesNumer = 0;
                this.visibleArrowUp = false;
                this.clearChatList();
                msgDevLog.log('newMsgComing clearAll', msg);
                return;
            }

            let msguuid = msg.m?.uuid;

            if (msg.delSelf) {
                let curIdx = this.viewChatList.findIndex(a => a.uuid == msguuid);
                if (curIdx >= 0) {
                    this.viewChatList.splice(curIdx, 1);
                    let preMsgIdx = curIdx - 1;

                    if (preMsgIdx >= 0) {
                        const preMsg = this.viewChatList[preMsgIdx];
                        const nextMsg = this.viewChatList[preMsgIdx + 1];

                        if (preMsg && nextMsg) {
                            F.dateLogic(nextMsg, preMsg);
                            F.timeLogic(nextMsg, preMsg);
                        }
                    }

                    msgDevLog.log('newMsgComing delSelf', msg);
                    return;
                }
            }

            let last = this.viewChatList[this.viewChatList.length - 1];

            let stimeOfNewmsg = F.paths(msg, 'm', 'stime');
            let msgSeqOfNewmsg = F.paths(msg, 'm', 'msgSeq');

            let firstMsg = this.viewChatList[0];
            let firststime = F.paths(firstMsg, 'stime');

            if (msgSeqOfNewmsg && this.firstSeq) {
                if (msgSeqOfNewmsg < this.firstSeq) {
                    return;
                }
            } else if (firststime && stimeOfNewmsg < firststime) {
                return;
            }

            // 替换消息 删除消息或回复消息
            if (msg.m) {
                let spaceId = getSpaceId(msg.mcTo);
                if (this.viewChatList.find(a => a.uuid == msguuid)) {
                    msgDevLog.log('newMsgComing insteadMessage', msguuid);
                    let run = insteadMessage(msguuid, spaceId);
                    await run();
                    return;
                } else if (last && (stimeOfNewmsg < last.stime || msgSeqOfNewmsg < last.msgSeq) && source !== 'fileDialogDownload') {
                    // 如果是旧的消息
                    msgDevLog.log(
                        'oldmsg::' + msguuid + '->' + stimeOfNewmsg + '->' + last.stime + +'->' + msgSeqOfNewmsg + '->' + last.msgSeq
                    );

                    let oldMsglist = await this.fetchMessage({forceStime: last.stime}, 'after', msguuid, undefined, false);
                    let currentMsg = oldMsglist.find(item => item.uuid === msguuid);

                    const viewChatList = [...this.viewChatList];

                    console.log('oldMsgIndex oldMsglist.length', msguuid);

                    if (currentMsg) {
                        msgDevLog.log('oldMsgIndex oldMsglist.length', msguuid);
                        let insertIndex = 0;
                        viewChatList.every(function (item, index) {
                            if (item.msgSeq && msgSeqOfNewmsg) {
                                if (item.msgSeq <= msgSeqOfNewmsg) {
                                    insertIndex = index;
                                    return true;
                                } else {
                                    return false;
                                }
                            }
                            if (item.stime <= stimeOfNewmsg) {
                                insertIndex = index;
                                return true;
                            }
                            return false;
                        });
                        const preMsg = viewChatList[insertIndex];
                        const nextMsg = viewChatList[insertIndex + 1];
                        console.log(`🚀 ~ handleCurrentMsgList ~ preMsg:`, preMsg, currentMsg, nextMsg);

                        if (preMsg) {
                            F.timeLogic(currentMsg, preMsg);
                            F.dateLogic(currentMsg, preMsg);
                        }

                        if (nextMsg) {
                            F.timeLogic(nextMsg, currentMsg);
                            F.dateLogic(nextMsg, currentMsg);
                        }

                        viewChatList.splice(insertIndex + 1, 0, currentMsg);

                        this.viewChatList = viewChatList;
                    } else {
                        msgDevLog.warn('oldMsgIndex msg before:', msguuid);
                    }
                    // console.log('newMsgs', _.cloneDeepWith(newMsgs));
                    let el = F.paths(this, '$refs', 'container_view', '$el');

                    const scrollTopBefore = el.scrollTop;
                    const scrollHeightBefore = el.scrollHeight;

                    this.$nextTick(() => {
                        if (msg.__offlineMsg) {
                            const scrollHeightAfter = el.scrollHeight;
                            const heightDiff = scrollHeightAfter - scrollHeightBefore;

                            el.scrollTop = scrollTopBefore + heightDiff;
                        }
                        this.setLimitUserSelectCheckbox('a14');
                    });
                    this.isNewestView = true;
                    return;
                }
            }

            if (this.isNewestView == false) {
                console.warn('newMsgComing isNewestView', msguuid);
                msgDevLog.warn('newMsgComing isNewestView:' + msguuid + '->' + stimeOfNewmsg + '->' + last.stime);
                msgDevLog.warn('newMsgComing stimeOfNewmsg:', stimeOfNewmsg > last.stime);
                if (last && stimeOfNewmsg > last.stime && msg.f == this.hid) {
                    this.viewChatList = await this.fetchMessage({forceStime: 0});
                    this.$nextTick(() => {
                        this.setLimitUserSelectCheckbox('a14');
                    });
                    this.isNewestView = true;
                    this.scrollBottom();
                    msgDevLog.log('newmsg::' + msguuid + '->' + stimeOfNewmsg + '->' + last.stime);
                }
                return;
            }

            let stime;
            if (last == null) {
                stime = 0;
            } else {
                stime = last.stime;
            }

            let newMsgs = await this.fetchMessage({forceStime: stime}, 'before', msguuid);
            // console.log('newMsgs', _.cloneDeepWith(newMsgs));

            if (newMsgs.length) {
                let uuids = this.viewChatList.map(item => item.uuid);
                newMsgs = newMsgs.filter(item => !uuids.includes(item.uuid));
                uuids = null;

                if (newMsgs.length && appdataStorage.getItem('imSdkOpen')) {
                    const viewLastMsg = this.viewChatList.findLast(item => item.msgSeq);
                    const newListTopMsg = newMsgs.find(item => item.msgSeq);

                    if (newListTopMsg && viewLastMsg && newListTopMsg.lastSeq !== viewLastMsg.msgSeq) {
                        const region = {
                            key: uuidv4(),
                            msgSeqStart: viewLastMsg.msgSeq,
                            startSeqStime: viewLastMsg.stime,
                            startUUID: viewLastMsg.uuid,
                            msgSeqEnd: newListTopMsg.msgSeq,
                            endSeqStime: newListTopMsg.stime,
                            endUUID: newListTopMsg.uuid
                        };

                        msgDevLog.log('handleCurrentMsgList -> regionData', this.currentReadToken, region);
                        this.fetchRegionMessage(this.currentReadToken, defaultSpaceId(), region, 'after');
                    }
                }
            }
            // console.log('newMsgs11', _.cloneDeepWith(newMsgs));

            this.viewChatList.push(...newMsgs);
            this.$nextTick(() => {
                this.setLimitUserSelectCheckbox('a15');
            });
            this.watchKeyChange();

            last = null;
            msg = null;

            // console.log('在这之前准备发送已读');
            // 在当前会话，需发送同步多端已读状态
            // console.log('准备发送已读');
            if (!this.candown && !this.hideDialogUnRead) {
                this.readStatusMultiSync();
            }

            this.$nextTick(async () => {
                let el = F.paths(this, '$refs', 'container_view', '$el');

                // 窗口失焦 & 有滚动条
                if (!this.isWinFocus && el && el.scrollHeight > el.offsetHeight) {
                    this.setCurrentDialogBottom(false);
                }

                if (this.viewChatList.length < 14) {
                    // handle have read and unread when there isn't scrollbar
                    readMessageMetioned(el, this);
                }

                // console.log('this.visibleEmojiReply', this.visibleEmojiReply);

                if (this.visibleEmojiReply) {
                    msgDevLog.log('newMsgComing visibleEmojiReply', msguuid);
                    return;
                }

                if (newMsgs.map(a => a.peerId == this.hid).reduce((a, b) => a || b, false)) {
                    this.scrollBottom();
                } else {
                    if (this.candown == false) {
                        this.scrollBottom();
                    }
                }
                newMsgs = null;
            });
        },

        clearMsgQueue() {
            // console.log('clearMsgQueue', newMsgQueue.getTaskQueueCount());
            return newMsgQueue && newMsgQueue.clearTask();
        },

        addMsgQueue(msg) {
            // console.log('addMsgQueue', msg);
            return newMsgQueue.addTask(this.handleCurrentMsgList.bind(this), msg).catch(err => {
                console.error('newMsgQueue catch：', err);
                msgDevLog.error('newMsgComing newMsgQueue catch：', msg.m?.uuid, err);
            });
        },
        // 更新会议助手卡片rsvp的状态
        onUpdateMeetingCardStatus({conferenceId, authorizer, cycleSubConfID, options}) {
            if (this.actDialogId !== 'AAAAAAAMWh0') return;
            // 更新视图
            this.viewChatList.forEach((item, index) => {
                if (
                    item?.plainMsg?.meta?.conferenceID === conferenceId &&
                    item?.plainMsg?.meta?.authorizer === authorizer &&
                    (!cycleSubConfID || item?.plainMsg?.meta?.cycleSubConfID === cycleSubConfID)
                ) {
                    const meta = Object.assign({}, this.viewChatList[index].plainMsg.meta, options);
                    this.$set(this.viewChatList[index].plainMsg, 'meta', meta);
                }
            });
        },

        onUpdateApprovalCardStatus({taskId, options}) {
            if (this.actDialogId !== 'AAAAAAAMWoE') return;

            this.viewChatList.forEach((item, index) => {
                if (item?.plainMsg?.meta?.taskId === taskId) {
                    const meta = Object.assign({}, this.viewChatList[index].plainMsg.meta, options);
                    this.$set(this.viewChatList[index].plainMsg, 'meta', meta);
                }
            });
        },

        onUpdateMessageAssertPath({uuid, dialogId, assertPath}) {
            if (this.actDialogId !== dialogId) return;
            const index = this.viewChatList.findIndex(item => item.uuid === uuid);
            if (index >= 0) {
                this.$set(this.viewChatList[index].plainMsg, 'assertPath', assertPath);
            }
        },

        onUpdateMessageSeq({uuid, dialogId, msgSeq, lastSeq}) {
            if (this.actDialogId !== dialogId) return;
            const index = this.viewChatList.findIndex(item => item.uuid === uuid);
            if (index >= 0) {
                this.$set(this.viewChatList[index], 'msgSeq', msgSeq);
                this.$set(this.viewChatList[index], 'lastSeq', lastSeq);
                this.$set(this.viewChatList[index].plainMsg, 'msgSeq', msgSeq);
                this.$set(this.viewChatList[index].plainMsg, 'lastSeq', lastSeq);
            }
        },

        updateSessionUnread() {
            let el = F.paths(this, '$refs', 'container_view', '$el');
            if (this.badgeCount && el && el.scrollHeight <= el.offsetHeight) {
                this.readStatusMultiSync();
            }
        },
        deleteMsg(tempList) {
            try {
                console.log('RightContentPanel delete-messsage', tempList);
                // const uuids = tempList.map(item => item.uuid);
                let viewChatList = this.viewChatList;

                // viewChatList = this.viewChatList.filter(item => !uuids.includes(item.uuid));

                tempList.forEach(temp => {
                    let curIdx = viewChatList.findIndex(item => temp.uuid === item.uuid);
                    if (curIdx >= 0) {
                        viewChatList.splice(curIdx, 1);
                        let preMsgIdx = curIdx - 1;

                        if (preMsgIdx >= 0) {
                            const preMsg = this.viewChatList[preMsgIdx];
                            const nextMsg = this.viewChatList[preMsgIdx + 1];

                            if (preMsg && nextMsg) {
                                F.dateLogic(nextMsg, preMsg);
                                F.timeLogic(nextMsg, preMsg);
                            }
                        }
                    }
                });

                this.viewChatList = viewChatList;

                if (F.paths(this.currentDialogReplyInfo, 'replyPannelVisible')) {
                    let replyChatInfo = this.currentDialogReplyInfo.replyChatInfo;
                    const uuids = tempList.map(item => item.uuid);
                    if (uuids.includes(replyChatInfo?.uuid)) {
                        this.clearReplyPannel();
                    }
                }
            } catch (error) {
                console.error('RightContentPanel delete-messsage error', error);
            }
        },
        resendFailMessage() {
            try {
                const mStatus = this.mStatus;
                console.log('resendFailMessage', mStatus);

                const resendList = this.viewChatList.filter(item => {
                    return mStatus[item.uuid] === 2;
                });

                console.log('resendList', resendList);
                for (let index = 0; index < resendList.length; index++) {
                    const element = resendList[index];
                    this.reSendMessage(element.uuid, true);
                }
            } catch (error) {
                console.error('resendFailMessage', error);
            }
        },
        resizeWin() {
            const box = document.getElementById('rightContentPannelDom');
            // console.log('box: ', box);

            const per = appdataStorage.getItem('currentInputHeightPercent');
            // console.log('per: ', per);

            if (box && per > 0) {
                const header = box.getElementsByClassName('dialog-head-container')[0];
                // console.log('header: ', header);
                if (header) {
                    const height = (box.offsetHeight - header.offsetHeight) * per;
                    this.currentInputHeight = Math.max(height, 128);
                    // console.log('this.currentInputHeight: ', this.currentInputHeight);
                }
            }
        },
        editTextMessage(e) {
            if (this.editMessageState.uuid) {
                this.$confirm(this.$t('ReEdit.EditWarning'), this.$t('ReEdit.EditThisMessage'), {
                    confirmButtonText: this.$t('ReEdit.GotIt'),
                    customClass: 'myconfirm',
                    showCancelButton: false,
                    showClose: false
                });
            } else {
                this.editMessageState.uuid = e.uuid;
                this.editMessageState.stime = e.stime;
            }
        },
        onMessageEditMetion(event, matches) {
            this.$refs.messageInputer.editSuggestionHandle(event, matches);
        },
        onSuggestSelect(e) {
            this.$refs.rightMessageEdit.selectMemberHandle(e);
        },
        onMessageEditClear() {
            this.editMessageState.uuid = '';
            this.editMessageState.stime = '';
        },

        async onMessageEditSend(element, isFail) {
            Caught.errorWith(
                async () => {
                    let current = this.$store.state.uiControl.actDialogId;
                    await Peer.checkIfsendMsg(current);

                    const text = this.$refs.messageInputer.getRichValue(element);

                    if (!isFail) {
                        this.sendMsgHandle(text, false, true);
                    } else {
                        const index = this.viewChatList.findIndex(({uuid}) => {
                            return uuid === this.editMessageState.uuid;
                        });
                        if (index !== -1) {
                            this.viewChatList.splice(index, 1);
                        }
                        console.log(`🚀 ~ index:`, index);

                        await Peer.deleteAMessage(this.editMessageState.uuid, defaultSpaceId());
                        this.editMessageState.uuid = '';
                        this.editMessageState.stime = '';

                        this.sendMsgHandle(text);
                    }
                },
                {loading: 'custom'}
            );
        },

        retrySendMessage(info) {
            this.operateMessageHandle({
                uuid: info.uuid,
                operate: 'resend',
                chatComponentType: info.chatComponentType
            });
        },

        emojiSelectForRightOption(sticker) {
            this.closeRightPanel();

            let operation = 1;

            const stickerRepliedList = this.currentChatInfo?.plainMsg?.m?.meta?.stickerRepliedList || [];
            const curMultiSpaceId = `${defaultSpaceId()}#${hidToNumber(this.$store.state.userInfo.hid)}`;

            for (let i = 0; i < stickerRepliedList.length; i++) {
                const stickerItem = stickerRepliedList[i];
                if (stickerItem.sticker === sticker) {
                    operation = stickerItem.participant[curMultiSpaceId] ? 0 : 1;
                    break;
                }
            }

            const msgTemplate = new MsgTemplate();
            const sendTo = this.actDialogId;
            const newData = {
                sticker,
                uuid: this.currentChatInfo.uuid,
                rtime: getTimestamp(),
                rname: renderName(this.$store.state.userInfo), // 回复参与者昵称
                operation
            };
            const isE2EE = !this.actDialogIdIsGroup && defaultSpaceId() && defaultSpaceId() == personalSpaceId();
            const sendData = msgTemplate.getEmojiReplyTemplate(sendTo, newData, isE2EE);
            Caught.errorWith(async () => {
                F.checkCurrentSpaceExpire();
                await Peer.checkIfsendMsg(sendTo).catch(error => {
                    console.error('[error]: ', 'error :>> ', error);
                    throw error;
                });
                Bus.$emit('emoji-reply', {
                    payload: sendData,
                    cmd: 'HyperText',
                    record: {sticker, operation}
                });
            });
        },
        async handleFileDownloadFail({uuid, spaceId}) {
            this.$store.commit('fileWorking/delFileWorkObj', uuid);
            let msg = await Peer.fetchMessageByUUID(uuid, spaceId);
            msg._key = uuid + '-' + new Date().valueOf();
            window.modifyMessageInViewBy(uuid, msg);
        },
        onRefreshDescribe() {
            if (this.isUser) {
                this.refreshFriendDescribe(this.currentReadToken, this.currentSpaceId);
            }
        },
        async refreshFriendDescribe(currentReadToken, spaceId) {
            try {
                if (currentReadToken === this.currentReadToken) {
                    const peer = await getPeerDescribe({
                        hid: currentReadToken,
                        spaceId
                    });
                    this.$store.dispatch('peerCollection/changePeerProps', {
                        hid: currentReadToken,
                        props: {
                            ...peer
                        },
                        spaceId
                    });
                }
            } catch (error) {
                msgDevLog.error('refreshFriendDescribe error', currentReadToken, error?.message);
                console.error(`🚀 ~ refreshFriendDescribe ~ error:`, error);
            }
        },
        getServerMaxSeq() {
            // conversationGetMaxId({
            //     conversationId: this.actDialogUid
            // }).then(res => {
            //     console.log(`🚀 ~ getServerMaxSeq ~ res:`, res);
            // });

            checkDialogMessage(this.actDialogId, defaultSpaceId(), this.mySession?.maxSeq);
        }
    },

    async beforeCreate() {
        let spaceId = defaultSpaceId();
        this.mineInfo = await mangePeerList(spaceId, [this.$store.state.userInfo.hid], 0);
    },

    created() {
        this.isHideMainWindow = this.getHideMainWindow();
        // 更新设置表情面板的默认值
        this.setVisibleEmojiReply(false);

        //右键另存为
        ipcRenderer.removeAllListeners('SAVE-AS-PIC-Res');
        ipcRenderer.on('SAVE-AS-PIC-Res', (ipc, message, error) => {
            console.warn('SAVE-AS-PIC-Res ===>', message, error);
            if (message === 'success') {
                this.$message({
                    message: this.$t('right_content_panel.save_file_success'),
                    type: 'success'
                });
            } else if (message === 'error') {
                if (error) {
                    this.$message({
                        message: this.$t('errorCode.error_space_noleft'),
                        type: 'error'
                    });
                } else {
                    this.$message({
                        message: this.$t('right_content_panel.save_file_fail'),
                        type: 'error'
                    });
                }
            }
        });
    },
    async mounted() {
        let spaceId = defaultSpaceId();
        Bus.$on('window-resize', this.resizeWin);
        this.resizeWin();
        ipcRenderer.on('ipcSendGetPreceiptView', () => {
            this.watchFocusView();
        });
        newMsgQueue = createQueue({name: 'newMsgQueue', maxTask: 1, interval: null, hooks: false});

        newMsgQueue.hooks.lastTaskAfter(function (res) {
            // console.log(`--newMsgQueue lastTaskAfter---`, res);
        });
        // 更改正在显示的消息
        window.modifyMessageInViewBy = async (uuid, msg) => {
            if (msg?.MIMETYPE === 'application/withdraw' && uuid === this.editMessageState?.uuid) {
                this.editMessageState.uuid = '';
                this.editMessageState.stime = '';
            }

            // Find the message index displayed on the page according to UUID
            // Because a UUID can only be a message, it is just a variable, not a list of array.

            // Cache view object, avoid multiple operations in a short time
            let viewChatList = [...this.viewChatList];

            viewChatList.map((item, iIndex) => {
                if (item.uuid === uuid) {
                    // fix: Re-edit message re-rendered.
                    const newItem = renderMsgItem(decodeMessageTransform(msg));

                    if (newItem?.plainMsg?.meta?.editInfo?.editedMsgStime) {
                        viewChatList[iIndex] = {
                            ...viewChatList[iIndex],
                            ...newItem,
                            isShowTimeStamp: viewChatList[iIndex].isShowTimeStamp,
                            _key: newItem.uuid + newItem?.plainMsg?.meta?.editInfo?.editedMsgStime
                        };
                    } else {
                        viewChatList[iIndex] = {
                            ...viewChatList[iIndex],
                            ...newItem,
                            _key: msg._key
                        };
                    }
                }

                // Because a UUID corresponding to uuidrePlied may be multiple messages
                if (item.uuidReplied === uuid) {
                    // Modify the message of the reply message
                    const newItem = {...viewChatList[iIndex]};
                    newItem.repliedInfo = renderMsgItem(decodeMessageTransform(msg));
                    viewChatList[iIndex] = newItem;
                }
            });
            // Update view
            this.viewChatList = viewChatList;

            let mIndex = this.viewChatList.findIndex(item => item.uuid === uuid);

            if (mIndex >= 0) {
                const current = renderMsgItem(decodeMessageTransform(msg));
                const next = this.viewChatList[mIndex + 1] || {stime: 0};
                const prev = this.viewChatList[mIndex - 1] || {stime: 0};
                F.dateLogic(current, prev);
                F.timeLogic(current, prev);
                // fix bug 6834
                const [mItem] = await this.handleReplyMessage([current]);
                this.$set(this.viewChatList, mIndex, {...this.viewChatList[mIndex], ...mItem});
                if (next.stime != 0) {
                    F.timeLogic(next, current);
                    F.dateLogic(next, current);
                    this.$set(this.viewChatList, mIndex + 1, next);
                }
            }

            this.$nextTick(() => {
                this.setLimitUserSelectCheckbox('a13');
            });
        };

        Bus.$on('delete-messsage', this.deleteMsg);
        Bus.$on('SET_MESSAGE_TO_BE_FORWARD', this.gotMembersToBeForward);
        ipcRenderer.on('COMBINE_EVENT_FORWAED', this.eventMembersToBeForward);
        Bus.$on('SET_MESSAGE_TO_BE_COMBINE_FORWARD', this.gotMembersToBeCombineForward);
        Bus.$on('OPEN_TO_BE_COMBINE_FORWARD', this.openToBeCombineForward);

        Bus.$on('set-message-unread-receipt', (viewChatList, editType) => {
            // console.log('set-message-unread-receipt', viewChatList, editType);
            viewChatList.forEach(item => {
                try {
                    const {uuid: msguuid} = item;
                    let mIndex = this.viewChatList.findIndex(message => message.uuid === msguuid);
                    let next = this.viewChatList[mIndex];
                    if (mIndex !== -1) {
                        if (editType === 'deleteAnnouncement') {
                            next.plainMsg.m.meta.announcementInfo.delete = true;
                            this.$set(this.viewChatList, mIndex, next);
                            return;
                        }

                        if (editType === 'receiptsSwitch') next.plainMsg.receiptShow = 'hide';
                        if (['receiptRead', 'receiptGroup'].includes(editType) && (next.plainMsg.messageStatus !== 3 || next.isFail)) {
                            next.plainMsg.messageStatus = 3;
                            next.isFail = false;
                            this.$store.dispatch('messageCollection/haveSent', msguuid);

                            if (getSpaceId(item.mcTo) === spaceId) {
                                this.$store.commit('dialogList/makeItReact', {
                                    dialogItem: {
                                        hid: this.currentReadToken,
                                        spaceId
                                    },
                                    spaceId,
                                    location: 25
                                });
                                let fn = window['newmsg__' + this.currentReadToken];
                                if (fn) fn();
                            }
                        }
                        if (editType === 'receiptsNoSwitch' || editType === 'receiptRead') next.plainMsg.receiptShow = 'show';
                        if (editType !== 'receiptsNoSwitch') next.plainMsg.unreadReceipt = editType !== 'receiptSend' ? '2' : '1';
                        if (editType === 'receiptGroup') {
                            next.plainMsg.meta = item.meta;
                            next.plainMsg.m.meta = item.meta;
                        }
                        this.$set(this.viewChatList, mIndex, next);
                    }
                } catch (error) {
                    console.error('newMsgComingNext error', error);
                }
            });
        });

        Bus.$on('set-message-preview', msg => {
            try {
                // console.log('set-message-preview', msg);

                const {uuid} = msg;
                const chatView = this.$refs[uuid];
                // console.log('chatView', chatView);
                if (chatView) {
                    chatView.chatInfo.plainMsg.m.meta.preview = true;
                    chatView.startDestroy = true;
                }
            } catch (error) {
                console.error('set-message-preview error', error, msg);
            }
        });
        let atomRun = F.globalAtomRun;

        //新消息
        window.newMsgComing = async (msg, refreshShortMsg = true) => {
            console.log('newMsgComing', _.cloneDeepWith(msg));
            try {
                atomRun(async () => {
                    let key = 'newmsg__' + msg.dialogId;
                    if (window[key] && refreshShortMsg) window[key](msg);

                    key = null;
                });

                if (msg.dialogId != this.currentReadToken) return;
                if (msg.burned) return;
                if (getSpaceId(msg.mcTo) !== this.currentSpaceId) return;

                // this.handleCurrentMsgList(msg);
                msgDevLog.log('newMsgComing addMsgQueue', msg.dialogId, msg.m?.uuid);
                this.addMsgQueue(msg);
            } catch (e) {
                console.error('newMsgComing error', msg, e);
                msgDevLog.error('newMsgComing error', msg, e, e.message);
            }
        };

        // 转发消息管理
        this.queueManager = new QueueManager(1);

        Bus.$on('notify', () => {
            this.contactCard.visible = false;
        });
        Bus.$on('group-pin-forward', this.forwardTextMessage);
        Bus.$on('close-all-pannel', this.closeGroupPannel);
        Bus.$on('operate-chat-message', this.operateMessageHandle);
        // 语音消息连续播放
        Bus.$on('next-voice', this.voiceNext);
        // 更新rsvp状态
        Bus.$on('meeting-status-update', this.onUpdateMeetingCardStatus);

        // Update Approval Card
        Bus.$on('approval-status-update', this.onUpdateApprovalCardStatus);

        Bus.$on('assert-parth-update', this.onUpdateMessageAssertPath);

        Bus.$on('message-seq-update', this.onUpdateMessageSeq);

        // keep-alive 缓存的组件激活时调用
        window.activedForChatFn = () => {
            if (this.$refs.container_view) {
                this.$refs.container_view.$el.scrollTo(0, 100000);
            }
        };

        containerDom = this.$refs.container_view.$el;
        this.addEventlistener('body', 'click', () => {
            // this.$store.commit('uiControl/SET_CURRENTEMOJIPANEL',false)
        });
        // this.$refs.messageInputer && this.messageInputFocus(this.$refs.messageInputer)
        this.onMessageInputFocus();

        // 点击非右键关闭右键弹出层
        document.body.addEventListener('click', this.closeRightPanel);

        // 滚动消息监听
        Bus.$on('scroll-message', this.scrollViewHandle);

        Bus.$on('reply-right-option-visable', this.closeRightPanel);

        Bus.$on('change-file-dialog-visible', () => {
            this.notOrientation = false;
        });

        // Bus.$on('online-resend-message', this.resendFailMessage);

        Bus.$on('message-input-focus', this.onMessageInputFocus);
        ipcRenderer.on('PASTE_CAPTURE_SEND_IMG', this.pageCaptureSendImg);
        // ipcRenderer.on('capture-screen-ok', () => {
        //     console.warn('capture-screen-ok');
        //     console.timeEnd('capture-screen000');
        // });

        (async () => {
            try {
                if (this.actDialogId) {
                    let isGroup = dataUtil.getPeerType(this.actDialogId) === 'group';

                    if (isGroup) {
                        await Peer.fetchWholeForChannel(this.actDialogId, spaceId);
                    } else {
                        await Peer.fetchWholeForContact(this.actDialogId, spaceId, 0, true);
                    }
                }
            } catch (error) {
                console.error('[error]: ', 'right error', error);
            }
        })();

        Bus.$on('SET_READED_UUID', this.setReadedUuid);
        this.debouncedReadedUuids = _.debounce(this.changeReadedUuids, 1000, {maxWait: 1000, leading: false, trailing: true});

        window.addEventListener('focus', this.updateSessionUnread);

        Bus.$on('operate-reply', this.replyHandler);
        Bus.$on('operate-open-right-panel', this.openRightPanel);
        Bus.$on('file-download-fail', this.handleFileDownloadFail);

        Bus.$on('refresh_destruct', this.onRefreshDescribe);
        Bus.$on('start_screen_capture', this.startScreenCapture);
    },
    beforeDestroy() {
        this.queueManager = null;
        newMsgQueue = null;
        Bus.$off('change-file-dialog-visible');
        Bus.$off('window-resize', this.resizeWin);
        Bus.$off('notify');
        Bus.$off('delete-messsage', this.deleteMsg);
        Bus.$off('SET_MESSAGE_TO_BE_FORWARD', this.gotMembersToBeForward);
        Bus.$off('SET_MESSAGE_TO_BE_COMBINE_FORWARD', this.gotMembersToBeCombineForward);
        Bus.$off('OPEN_TO_BE_COMBINE_FORWARD', this.openToBeCombineForward);
        Bus.$off('group-pin-forward', this.forwardTextMessage);
        Bus.$off('operate-chat-message', this.operateMessageHandle);
        // Bus.$off('online-resend-message', this.resendFailMessage);
        Bus.$off('close-all-pannel', this.closeGroupPannel);
        Bus.$off('scroll-message', this.scrollViewHandle);
        Bus.$off('message-input-focus', this.onMessageInputFocus);
        Bus.$off('reply-right-option-visable', this.closeRightPanel);
        Bus.$off('SET_READED_UUID', this.setReadedUuid);
        Bus.$off('meeting-status-update');
        Bus.$off('set-message-unread-receipt');
        Bus.$off('operate-reply', this.replyHandler);
        Bus.$off('operate-open-right-panel', this.openRightPanel);
        Bus.$off('file-download-fail', this.handleFileDownloadFail);
        Bus.$off('refresh_destruct', this.onRefreshDescribe);
        Bus.$off('approval-status-update', this.onUpdateApprovalCardStatus);
        Bus.$off('assert-parth-update', this.onUpdateMessageAssertPath);
        Bus.$off('message-seq-update', this.onUpdateMessageSeq);

        this.debouncedReadedUuids = null;

        ipcRenderer.removeListener('PASTE_CAPTURE_SEND_IMG', this.pageCaptureSendImg);
        this.removeEventListener('body', 'click', () => {
            // this.$store.commit('uiControl/SET_CURRENTEMOJIPANEL',false)
        });
        document.body.removeEventListener('click', this.closeRightPanel);
        window.removeEventListener('focus', this.updateSessionUnread);
        Bus.$off('start_screen_capture', this.startScreenCapture);
    }
};
function insteadMessage(uuid, spaceId) {
    return async () => {
        let msg = await Peer.fetchMessageByUUID(uuid, spaceId);
        // console.log('insteadMessage', msg.isFail, msg.messageStatus);
        if (msg) {
            window.modifyMessageInViewBy(uuid, msg);
        }
    };
}
</script>
<style lang="scss">
.message-loading {
    width: 100%;
    height: 20px;
    margin-left: 6px;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}
.el-popper.popover-screen-capture {
    border-radius: 4px;
    background-color: var(--grey-9);
    padding: 7px 8px;
    font-family: var(--font-family-normal);
    font-size: 12px;
    line-height: 14px;
    color: var(--grey-4);
    margin-bottom: 0px;
    margin-top: 8px;
    box-shadow: var(--box-shadow-select-dropdown);
    border: none;
    &.down-icon {
        padding: 2px 0;
        background-color: var(--grey-0);
        .form-wrap {
            padding: 9px 16px;
            &:hover {
                background-color: var(--grey-1);
            }
            &.is-checked {
                // background-color: var(--grey-2);
            }
        }
        .el-checkbox__label {
            color: var(--grey-10);
            font-size: 14px;
            line-height: 18px;
            padding-left: 12px;
        }
    }
    .el-popover__title {
        margin-bottom: 0px;
        font-family: var(--font-family-normal);
        font-size: 12px;
        line-height: 14px;
        color: var(--grey-0);
    }
}

.download-file-dialog {
    width: 456px;
    border-radius: 10px;
    .el-dialog__title {
        font-family: var(--font-family-normal);
        font-size: 16px;
        font-weight: 600;
        line-height: 1.25;
        text-align: left;
        color: var(--grey-10);
    }
    .el-button {
        border-radius: 4px;
        background-color: var(--grey-2);
        font-family: var(--font-family-normal);
        height: 32px;
        font-size: 14px;
        text-align: center;
        color: var(--grey-10);
        line-height: 6px;
        &:hover {
            background: var(--grey-3);
            border: 1px solid var(--grey-3);
        }
    }
    .el-button--primary {
        background-color: var(--blue-4);
        color: var(--grey-0);
        &:hover {
            background: var(--blue-5);
            border: 1px solid var(--blue-5);
        }
    }
    .el-dialog__body {
        padding: 16px 20px;
        font-size: 14px;
        line-height: 1.29;
        color: var(--grey-10);
    }
    .el-dialog__header {
        border-bottom: 1px solid var(--grey-1);
    }

    .content {
        .icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--orange-4);
            color: var(--grey-0);
            font-size: var(--font-size-10);
            text-align: center;
        }
        .title {
            font-family: var(--font-family-normal);
            font-size: 14px;
            line-height: 1.29;
            text-align: left;
            color: var(--grey-10);
            word-break: keep-all;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        .disk-tips {
            color: var(--red-4);
            margin-top: 10px;
        }
        .location {
            display: flex;
            flex-direction: row;
            margin-top: 6px;
            position: relative;

            .download-path {
                width: 316px;
                height: 32px;
                line-height: 32px;
                padding: 0px 7px;
                border-radius: 4px;
                border: solid 1px var(--grey-2);
                background-color: var(--grey-1);

                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .download-file-btn {
                border-radius: 4px;
                background-color: var(--grey-2);
                vertical-align: middle;
                text-align: center;
                padding: 8px 28px;
                cursor: pointer;
                margin-left: 4px;
                &:hover {
                    background: var(--grey-3);
                }
            }
        }
        .tip {
            display: flex;
            margin-top: 16px;
            margin-left: 4px;
        }
        .perference-tip {
            font-family: var(--font-family-normal);
            font-size: 12px;
            font-weight: 500;
            line-height: 1.17;
            text-align: left;
            color: var(--orange-4);
            margin-left: 4px;
        }
    }
}
.group-pin-top-ten-dialog {
    border-radius: 10px;
    max-width: 456px;
    .el-dialog__title {
        font-family: var(--font-family-normal);
        font-size: 14px;
        font-weight: 500;
        line-height: 1.29;
        color: #141414;
    }
    .el-dialog__body {
        font-family: var(--font-family-normal);
        background-color: var(--grey-1);
        font-size: 14px;
        line-height: 1.29;
        color: var(--grey-8);
        padding: 16px 24px;
    }
    .el-dialog__footer {
        background-color: var(--grey-1);
        border-radius: 0px 0px 10px 10px;
    }
    .el-button {
        padding: 8px 20px;
        font-size: 14px;
        border-radius: 4px;
        background-color: var(--blue-4);
        color: #fff;
        width: 88px;
    }
    .body {
        word-break: break-word;
        font-size: 13.6px;
    }
}
.group-unpin-dialog {
    border-radius: 10px;
    max-width: 456px;
    .el-dialog__title {
        font-family: var(--font-family-normal);
        font-size: 14px;
        font-weight: 500;
        line-height: 1.29;
        color: var(--grey-10);
    }
    .el-dialog__body {
        font-family: var(--font-family-normal);
        background-color: var(--grey-1);
        font-size: 14px;
        line-height: 1.29;
        color: var(--grey-8);
        padding: 16px 24px;
    }
    .el-dialog__footer {
        background-color: var(--grey-1);
        border-radius: 0px 0px 10px 10px;
    }
    .el-button {
        padding: 8px 20px;
        font-size: 14px;
        border-radius: 4px;
        background-color: var(--blue-4);
        color: #fff;
        min-width: 88px;
        outline: none;
    }
    .cancelBtn {
        background-color: #d9d9d9;
        color: var(--blue-4);
        border: none;
    }
    .body {
        word-break: break-word;
    }
}

.view-scroll-wrap.isMultipleSelected {
    .e2e_wrap {
        pointer-events: none;
        user-select: none;
        margin: 0px 100px;
    }
}
.popover-contact {
    padding: 0;
    border: 0px solid transparent;
    background-color: transparent;
    box-shadow: none;
}
.chats_-tooltip {
    margin-top: 8px !important;
    padding: 7px 8px !important;
}
</style>
<style lang="scss" scoped>
.suggestion-backtop {
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 100%;
}
.select-following {
    position: absolute;
    top: 68px;
    width: 100%;
    height: 40px;
    padding: 0 24px;
    box-sizing: border-box;
    .select-box {
        position: absolute;
        border-radius: 8px;
        border: 1px solid var(--mv2-Grey-Grey4, #bfbfbf);
        background: var(--mv2-Grey-Grey0, #fff);
        display: flex;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        left: 50%;
        transform: translate(-50%, 0);
        z-index: 99;
    }
    &:after {
        content: '';
        width: 100%;
        height: 1px;
        position: absolute;
        top: 20px;
        background: var(--mv2-Grey-Grey4, #bfbfbf);
    }
}
.select-footer-bar {
    padding: 0 24px;
    border-top: 1px solid var(--grey-2);
    box-sizing: border-box;
    height: 74px;
    background-color: var(--grey-0);
    .flex-around-center {
        flex-grow: 1;
    }
    .select-share-item {
        text-align: center;
    }
    .select-item-icon {
        width: 32px;
        height: 32px;
        padding: 8px;
        box-sizing: border-box;
        border-radius: 10px;
        background: var(--grey-1);
        margin: 0 auto 4px;
        :deep(.svg-icon) {
            color: #434343;
        }
    }
}
.menu {
    position: relative;
}

.chatmessage-container {
    // padding-left: 24px;
    // padding-right: 24px;
    // box-sizing: border-box;
}
.dialog-content {
    position: relative;
    height: 65vh;
    width: 100%;
    .chatscroll-container {
        position: relative;
        height: 100%;
        width: calc(100% - 6px);
        padding-top: 35px;
        padding-bottom: 30px;
        box-sizing: border-box;
    }
    .message_cover {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 1);
    }
    .above_box {
        position: absolute;
        bottom: 117px;
        right: 15px;
        width: 99%;
    }
}
.svgicon {
    font-size: var(--font-size-46);
    color: #ff9500;
}
.dialog-container {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    background-color: var(--grey-0);
    flex: 1;
    overflow: hidden;
    border-right: 2px solid var(--grey-0);
}
.not_readlistinfo {
    margin: 0 auto;
    display: flex;
    width: 90%;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .listinfo {
        margin: 0 10px;
        width: 45%;
        text-align: center;
        box-sizing: border-box;
        .scroll-content-wrap {
            margin: 0 auto;
            height: 600px;
            overflow-x: hidden;
            overflow-y: scroll;
        }
        .list-wrap-item {
            padding-bottom: 30px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            .nickname {
                display: inline-block;
                padding: 0 3rem 0 1rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
.message-hidden-input {
    position: absolute;
    top: 0;
    left: -9999999999px;
    padding: 0;
    margin: 0;
    width: 0;
    height: 0;
}
.message-box {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    &.show {
        transform: translate(0px, 1000px);
    }
    &.showed {
        transition: all 0.1s linear;
        transform: translate(0px, 0px);
    }
    .view-scroll-wrap {
        width: 100%;
        flex: 1;
        overflow: hidden;
    }
    .message-list {
        width: 100%;
        height: 647px;
        overflow-y: auto;
        background: var(--grey-0);

        .chat-item-box {
            display: flex;
            justify-content: flex-start;
            align-items: flex-end;
            padding: 0px 20px;
            margin-bottom: 3px;

            .chat-portrait {
                width: 30px;
                height: 30px;
                padding: 0px 6px;
                margin: 0px;
            }
            .chat-item {
                position: relative;
                max-width: 420px;
                min-width: 10px;
                min-height: 10px;
                border-radius: 6px;
                background-color: var(--grey-1);
                padding: 8px 15px;
                &_nickname {
                    font-size: var(--font-size-12);
                    color: var(--blue-4);
                }
                &:after {
                    content: '';
                    position: absolute;
                    bottom: 0px;
                    right: 0px;
                    left: -8px;
                    width: 10px;
                    height: 10px;
                    border: 10px solid transparent;
                    border-bottom: 10px solid var(--grey-1);
                }
            }
        }
        .chat-item-box--self {
            justify-content: flex-end;
            align-items: flex-end;
            .chat-item {
                background: var(--sky-blue-2);
                &:after {
                    content: '';
                    position: absolute;
                    bottom: 0px;
                    right: -8px;
                    left: unset;
                    width: 10px;
                    height: 10px;
                    border: 10px solid transparent;
                    border-bottom: 10px solid var(--sky-blue-2);
                }
            }
        }
        .split-message {
            margin-bottom: 10px;
        }
    }
    .message-sendbox-container {
        position: relative;
        width: 100%;
    }
    .message-sendboxwrap {
        box-sizing: border-box;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px 24px 20px 24px;
        // overflow: hidden;
        // 添加了左侧边栏，需在原来的基础上再减64px
        height: 170px;
        border-top: 1px solid var(--mv2-Grey-Grey2, #e9e9e9);
        z-index: 3;
        &.message-box-remove {
            display: flex;
            flex-direction: row;
            padding: 0 25px;
            justify-content: flex-start;
            align-items: center;
            .person-removed {
                color: var(--grey-5);
                font-size: var(--font-size-14);
            }
        }
        .message-sendbox {
            width: calc(100% - 56px);
            padding-right: 5px;
            height: 100%;
            font-size: var(--font-size-15);
            color: var(--grey-10);
            outline: none;
            overflow: auto;
            word-break: normal;
            &::placeholder {
                color: var(--grey-4);
            }
        }
        .message-sendbox-icon {
            position: absolute;
            bottom: 24px;
            right: 24px;
            width: 40px;
            height: 40px;
            background-color: #ccc;
            box-sizing: border-box;
            padding: 8px 7px 8px 9px;
            border-radius: 50%;
            .svg-icon {
                color: var(--grey-0);
            }
            &.active {
                background-color: var(--blue-4);
            }
        }
        .action-icon-group {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 22px;
            .chat_icon {
                width: 24px;
                height: 24px;
                margin-right: 21px;
                cursor: pointer;
                user-select: none;
                outline: none;
                background-repeat: no-repeat;
                background-position: center;
                border-radius: 4px;
                &:last-child {
                    margin-right: 0;
                }
                &.screen_capture_wrap {
                    width: 30px;
                    display: flex;
                    align-items: center;
                    flex-flow: row nowrap;
                    justify-content: space-between;
                    .screen-capture {
                        width: 24px;
                        height: 24px;
                        background: url('../assets/chat/msgInput/screen_capture.svg') no-repeat;
                        outline: none;
                        border-radius: 4px;
                        &:hover,
                        &.active {
                            background-color: var(--black-05);
                        }
                    }
                    .down-icon {
                        width: 12px;
                        height: 24px;
                        background: url('../assets/chat/msgInput/ic_back_hover.svg') no-repeat;
                        background-position: center;
                        border-radius: 4px;
                        outline: none;
                        margin-left: -2px;
                        &:hover,
                        &.active {
                            background-color: var(--black-05);
                        }
                    }
                }
            }
            .emoji_icon {
                background-image: url('../assets/chat/msgInput/emoji_icon.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .namecard_icon {
                background-image: url('../assets/chat/msgInput/namecard_icon.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .mention_icon {
                background-image: url('../assets/chat/msgInput/mention_icon.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .fileIcon_icon {
                background-image: url('../assets/chat/msgInput/ic_message_file.svg');
                background-size: 100% 100%;
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .imageIcon_icon {
                &:hover,
                &.active {
                    background-color: var(--black-05);
                }
            }
            .upimfile {
                font-size: 0;
                .icon {
                    width: 18px;
                    height: 18px;
                    cursor: pointer;
                }
            }
        }
    }

    .blocked-message-sendboxwrap {
        display: flex;
        align-items: center;
        justify-content: center;
        & .unblock-span {
            color: var(--blue-4);
            cursor: pointer;
        }
    }
    .message-sendboxwrap--faile {
        position: absolute;
        bottom: 0px;
        left: 0px;
        z-index: 4;
        height: 73px;
        opacity: 0;
    }
}
.inputStyle {
    position: absolute;
    left: 164px;
    width: 120px;
    display: none;
}
[dir='rtl'] .download-file-dialog .content .location .download-file-btn {
    padding: 8px 0;
    flex: 1;
}
</style>

