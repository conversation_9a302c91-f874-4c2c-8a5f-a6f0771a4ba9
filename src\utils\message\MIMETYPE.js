import {SearchEnum} from '@/enum/SearchStatus.js';
import {getSpaceId} from '@/utils/SpaceManager';
import {parseMarkdownToPlainText} from '@/utils/editor/parse.js';
import * as F from '@/utils/Function';

const imageMessageConfig = Object.freeze({
    isNeedFts: true,
    getPreviewReplied(message) {
        return 'Image';
    },
    getFtsMsg(message) {
        if (message.m && message.m.meta && message.m.meta.filename) {
            return [
                {
                    spaceId: getSpaceId(message.mcTo),
                    body: message.m.meta.filename,
                    type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                    subType: SearchEnum.ftsType.BUS_SUBTYPE_FILE,
                    busItemId: message.m.uuid,
                    timeStamp: message.m.stime
                }
            ];
        }

        return [];
    }
});

export const MIMETYPE_TYPE = Object.freeze({
    'application/announcement': {},
    'application/announcement/delete': {},
    'application/card': {},
    'application/destroy': {},
    'application/e2eemeeting': {},
    'application/pin': {},
    'application/receipt': {},
    'application/receipt/count': {},
    'application/restricted': {},
    'application/sticker-replied': {},

    'application/video': {
        isNeedFts: true,
        getFtsMsg(message) {
            if (message.m && message.m.meta && message.m.meta.filename) {
                return [
                    {
                        spaceId: getSpaceId(message.mcTo),
                        body: message.m.meta.filename,
                        type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                        subType: SearchEnum.ftsType.BUS_SUBTYPE_FILE,
                        busItemId: message.m.uuid,
                        timeStamp: message.m.stime
                    }
                ];
            }

            return [];
        },
        getPreviewReplied(message) {
            return message?.m?.meta?.filename;
        }
    },

    'application/withdraw': {},
    'withdraw/remind': {},
    'approval/card': {
        getPreviewReplied(message) {
            return 'Approval';
        }
    },

    'audio/voice-msg': {
        getPreviewReplied(message) {
            return message?.m?.meta?.duration?.toString();
        }
    },
    'call/record': {
        getPreviewReplied(message) {
            return message?.m?.meta?.callType;
        }
    },
    'edit/application/card': {},
    'edit/richtext/plain': {},
    'edit/robot/richtext': {},
    'edit/robot/text': {},
    'edit/text/plain': {},
    'forward/approval/card': {},
    'forward/robot/richtext': {},

    'image/jpeg': imageMessageConfig,
    'image/jpg': imageMessageConfig,
    'image/png': imageMessageConfig,
    'image/gif': imageMessageConfig,
    'image/webp': imageMessageConfig,
    'image/bmp': imageMessageConfig,

    'location/share': {
        getPreviewReplied(message) {
            return message?.m?.meta?.poiName || message?.m?.meta?.poiAddress;
        }
    },
    'longtext/plain': {
        getPreviewReplied(message) {
            return message?.m?.body;
        }
    },
    'meeting/card': {},
    'meeting/invite': {
        getPreviewReplied(message) {
            return message?.m?.meta;
        }
    },
    'poi/card': {},
    'richtext/plain': {
        isNeedFts: true,
        getFtsMsg(message) {
            const text = parseMarkdownToPlainText(message.m.body);
            const spaceId = getSpaceId(message.mcTo);
            const uuid = message.m.uuid;

            const res = [];

            if (text) {
                res.push({
                    spaceId: spaceId,
                    body: text,
                    type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                    subType: SearchEnum.ftsType.BUS_SUBTYPE_MESSAGE,
                    busItemId: uuid,
                    timeStamp: message.m && message.m.stime
                });
            }

            if (message.m.meta?.medias?.length) {
                message.m.meta?.medias.forEach(item => {
                    if (item.download?.fid && item.type === 'image') {
                        res.push({
                            spaceId: spaceId,
                            body: item.filename,
                            type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                            subType: SearchEnum.ftsType.BUS_SUBTYPE_FILE,
                            busItemId: uuid + '|' + item.download.fid,
                            timeStamp: message.m && message.m.stime
                        });
                    }
                });
            }

            return res;
        },
        getPreviewReplied(message) {
            return message?.m?.body;
        }
    },
    'robot/richtext': {
        getPreviewReplied(message) {
            return parseMarkdownToPlainText(message?.m?.body);
        }
    },
    'robot/text': {},
    'text/combine': {},
    'text/plain': {
        isNeedFts: true,
        getFtsMsg(message) {
            if (message.m && message.m.body) {
                const ftsBody = F.translateMsgMetionToAlias({
                    body: message?.m?.body,
                    ref: message?.m?.meta?.ref
                });

                return [
                    {
                        spaceId: getSpaceId(message.mcTo),
                        body: ftsBody,
                        type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                        subType: SearchEnum.ftsType.BUS_SUBTYPE_MESSAGE,
                        busItemId: message.m.uuid,
                        timeStamp: message.m.stime
                    }
                ];
            }

            return [];
        },
        getPreviewReplied(message) {
            return message?.m?.body;
        }
    },
    'text/vcard': {
        getPreviewReplied(message) {
            let hid = message?.m?.meta.contactUid;
            if (hid) {
                return hid;
            } else {
                let body = message.body;
                let colonIndex = body.lastIndexOf(':');
                let equalIndex = body.lastIndexOf('=');
                return body.substring(colonIndex + 1, equalIndex);
            }
        }
    },
    'x-filetransfer/octet-stream': {
        isNeedFts: true,

        getFtsMsg(message) {
            if (message.m && message.m.meta && message.m.meta.filename) {
                return [
                    {
                        spaceId: getSpaceId(message.mcTo),
                        body: message.m.meta.filename,
                        type: SearchEnum.ftsType.BUS_TYPE_MESSAGE,
                        subType: SearchEnum.ftsType.BUS_SUBTYPE_FILE,
                        busItemId: message.m.uuid,
                        timeStamp: message.m.stime
                    }
                ];
            }

            return [];
        },
        getPreviewReplied(message) {
            return message?.m?.meta?.filename;
        }
    },

    // Deprecation Start
    'application/eliminate': {},
    'application/pdf': {},
    'application/msword': {},
    'application/msexcel': {},
    'application/vnd.ms-excel': {},
    'application/vnd.ms-powerpoin': {},
    'application/octet-stream': {},
    'application/cmd': {},
    'application/alarm': {},
    'application/sticker': {},
    'text/richurl-x': {},
    'text/abstract': {}
    // Deprecation End
});

// public enum mediaMime: String {
//         case unknow = "unknow"
//         case textPlain = "text/plain"
//         case imageGif = "image/gif"
//         case imageJpeg = "image/jpeg"
//         case vcard = "text/vcard"
//         case receipt = "application/receipt"
//         case receiptCount = "application/receipt/count"
//         case withdraw = "application/withdraw"
//         case destroy = "application/destroy"
//         case voice = "audio/voice-msg"
//         case file = "x-filetransfer/octet-stream"
//         case meetingInvite = "meeting/invite"
//         case meetingCard = "meeting/card"
//         case callRecord = "call/record"
//         case stickReply = "application/sticker-replied"
//         case pin = "application/pin"
//         case restricted = "application/restricted"
//         case e2eeMeeting = "application/e2eemeeting"
//         case announcement = "application/announcement"
//         case announcementDeleted = "application/announcement/delete"
//         case location = "poi/card"
//         case liveLocation = "location/share"
//         case longText = "longtext/plain"
//         case richText = "robot/richtext"
//         case editRichText = "edit/robot/richtext"
//         case robotText = "robot/text"
//         case editRobotText = "edit/robot/text"
//         case forwardRichText = "forward/robot/richtext"
//         case inputIndicator = "Input_Life_Cycle"
//         case editTextPlain = "edit/text/plain"
//         case newRichText = "richtext/plain"
//         case editNewRichText = "edit/richtext/plain"
//         case cardMessage = "application/card"
//         case video = "application/video"
//         case approvalCard = "approval/card"
//         case forwardApproval = "forward/approval/card"
//         //以下都是未用到的之前的老类型
//         case eliminate = "application/eliminate"
//         case pdf = "application/pdf"
//         case msword = "application/msword"
//         case msexcel = "application/vnd.ms-excel"
//         case msppt = "application/vnd.ms-powerpoin"
//         case octetStream = "application/octet-stream"
//         case imagePng = "image/png"
//         case cmd = "application/cmd"
//         case alarm = "application/alarm"
//         case sticker = "application/sticker"
//         case richUrl = "text/richurl-x"
//         case abstract = "text/abstract"
//     }
