<template>
  <div>
    <CardText text="test" :isMainTitle="true" />
    <CardInput />
    <CardTitle />
    <CardButton text="test" type="info" :plain="true"/>
    <CardButton text="test" type="primary" :plain="true" />
    <CardButton text="test" type="danger" :plain="true" />
    <CardButton text="test" type="text" />
    <CardButton text="test" type="text" :isTypeTextBlue="true" />
    <CardButton text="test" type="text" :isTypeTextRed="true" />
    <CardButton text="test" type="primary" />
    <CardButton text="test" type="danger" />
    <CardButton text="test" type="info" />
    <CardList :listData="[1,2,3,4,5]" />
    <CardCheckGroup />
  </div>
</template>

<script>
  import CardText from '../components/CardText.vue';
  import CardInput from '../components/CardInput.vue';
  import CardTitle from '../components/CardTitle.vue';
  import CardButton from '../components/CardButton.vue';
  import CardList from '../components/CardList.vue';
  import CardCheckGroup from '../components/CardCheckGroup.vue';
  export default {
    name: 'index',
    components: {
      CardText,
      CardInput,
      CardTitle,
      CardButton,
      CardList,
      CardCheckGroup
    }
  }
</script>

<style lang="scss" scoped></style>