import CardButton from './CardButton.vue';
import CardCheckGroup from './CardCheckGroup.vue';
import CardContainer from './CardContainer.vue';
import CardImg from './CardImg.vue';
import CardInput from './CardInput.vue';
import CardList from './CardList.vue';
import CardPersonList from './CardPersonList.vue';
import CardTagList from './CardTagList.vue';
import CardText from './CardText.vue';
import CardTitle from './CardTitle.vue';

export default {
    CardButton,
    CardCheckGroup,
    CardContainer,
    CardImg,
    CardInput,
    CardList,
    CardPersonList,
    CardTagList,
    CardText,
    CardTitle
};