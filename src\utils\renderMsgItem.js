/**
 * @file renderMsgItem
 * <AUTHOR>
 */
import store from '@/store';
import i18n from '@/lang';
import {addFileProcessFileds} from '@/api/messageManger.js';

export const msgTypeInfo = {
    HyperText: 'HyperText',
    Event: 'Event',
    'text/plain': 'text',
    'image/jpeg': 'picture',
    'image/webp': 'picture',
    'image/jpg': 'picture',
    'image/gif': 'picture',
    'image/png': 'picture',
    'image/bmp': 'picture',
    'audio/voice-msg': 'Audio message',
    'text/vcard': 'Contact Card',
    'meeting/invite': 'Meeting Invite',
    'text/richurl-x': 'link',
    'text/richurl': 'link',
    'x-filetransfer/octet-stream': 'file message',
    'application/sticker': 'emoji',
    'application/alarm': 'calendar message',
    'meeting/card': 'Meeting Card',
    'approval/card': 'Approval Card',
    'forward/approval/card': 'Forward Approval Card',
    'poi/card': 'Map Card',
    'location/share': 'Location Share'
};
export const msgRestrictedTypeInfo = {
    'x-filetransfer/octet-stream': 'File Message',
    HyperText: 'HyperText',
    Event: 'Event',
    'text/plain': 'Text',
    'longtext/plain': 'LongText',
    'richtext/plain': 'RichText',
    'image/jpeg': 'Picture',
    'image/webp': 'Picture',
    'image/jpg': 'Picture',
    'image/gif': 'Picture',
    'image/png': 'Picture',
    'image/bmp': 'Picture',
    'audio/voice-msg': 'Audio message',
    'text/vcard': 'Contact Card',
    'meeting/invite': 'Meeting Invite',
    'text/richurl-x': 'Link',
    'text/richurl': 'Link',
    'text/combine': 'CombineText',
    'application/sticker': 'Emoji',
    'application/alarm': 'Calendar message',
    'application/announcement': 'Announcement',
    'application/video': 'Video',
    'robot/richtext': 'RobotRich',
    'robot/text': 'Text',
    'forward/robot/richtext': 'ForRobotRich',
    'forward/robot/text': 'Text',
    'edit/robot/text': 'Text',
    'edit/robot/richtext': 'RobotRich',
    'meeting/card': 'Meeting Card',
    'approval/card': 'Approval Card',
    'forward/approval/card': 'Forward Approval Card',
    'poi/card': 'Map Card',
    'location/share': 'Location Share'
};

const notificationToEventType = ['Destroy_Interval_Changed', 'Destroy_State_Changed'];

// 获取根据服务端限制发送消息显示文案
export function getSpaceLimitMsgText(MIMETYPE, isWrapper = true) {
    const currentSpaceMessageFilterArr = store?.getters['spaceLimit/currentSpaceMessageFilterArr'];
    const result = msgRestrictedTypeInfo[MIMETYPE];
    const output = {
        isIncluded: !!currentSpaceMessageFilterArr.find(
            item => item?.MIMETYPE && new RegExp(item.MIMETYPE).test(MIMETYPE) && item.DIRECTIONS === 2 && item.restricted
        ),
        showMsgText: result || ''
    };
    if (isWrapper && output.showMsgText) {
        output.showMsgText = `[${output.showMsgText}]`;
    }
    return output;
}

// 消息模板
export default function renderMsgItem(msgItem) {
    let template = {
        customData: {
            msg: msgItem.m.body
        },
        direct: msgItem.isMine ? 'LEFT' : 'LEFT',
        token: msgItem.peerId,
        chatComponentType: 'MESSAGE',
        timestamp: msgItem.m.stime,
        uuid: msgItem.m.uuid,
        isShowTimeStamp: msgItem.isShowTimeStamp,
        plainMsg: msgItem,
        stime: msgItem.stime,
        peerId: msgItem.peerId,
        msgSeq: msgItem.msgSeq,
        lastSeq: msgItem.lastSeq
    };

    if (msgItem.burned) {
        return template;
    }

    // #region START
    const limitConfig = getSpaceLimitMsgText(msgItem.m.MIMETYPE);
    if (limitConfig.isIncluded) {
        template.chatComponentType = 'RESTRICTED';
        template.customData.msg = limitConfig.showMsgText;
        return template;
    }
    // #endregion

    //isStar 表示失败意思 efailed
    if (msgItem.isStar) {
        template.chatComponentType = 'E2EDESCRIPT';
        template.customData.msg = 'Failed to decrypt this message';
        return template;
    }
    if (msgItem.c === 'E2EKeyChange') {
        template.chatComponentType = 'E2EKeyChangeTip';
        template.customData.msg = 'Key Change';
        return template;
    }
    if (msgItem.c === 'Notification' && notificationToEventType.includes(msgItem.m?.type)) {
        template.chatComponentType = 'EVENT';
        template.customData.type = msgItem.m.type;
        if (msgItem.m.body) {
            template.customData.msg = msgItem.m.body;
        }

        return template;
    }

    if (msgItem.m.MIMETYPE === 'location/share') {
        if (!msgItem.m.body) {
            msgItem.body = i18n.t('unsupported_message');
            msgItem.m.body = i18n.t('unsupported_message');
        }
        template.customData.msg = msgItem.m.body;
        return template;
    }

    if (msgItem.m.MIMETYPE === 'text/richurl-x') {
        template.chatComponentType = 'RICHURL';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'audio/voice-msg') {
        template.chatComponentType = 'VOICE';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'edit/application/card') {
        template.chatComponentType = 'MSGCARD';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'text/vcard') {
        template.chatComponentType = 'VCARD';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'meeting/invite') {
        template.chatComponentType = 'MEETINGINVITE';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'meeting/card') {
        template.chatComponentType = 'MEETINGCARD';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'approval/card') {
        template.chatComponentType = 'APPROVALCARD';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'forward/approval/card') {
        template.chatComponentType = 'APPROVALFORWARDCARD';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'poi/card') {
        template.chatComponentType = 'MAPVIEW';
        return template;
    }
    if (msgItem.m.MIMETYPE === 'location/share') {
        return template;
    }
    if (msgItem.m.MIMETYPE === 'call/record') {
        template.chatComponentType = 'CALLRECORD';
        return template;
    }
    if (
        msgItem.m.MIMETYPE === 'application/withdraw' ||
        msgItem.m.MIMETYPE === 'application/eliminate' ||
        msgItem.m.MIMETYPE === 'withdraw/remind'
    ) {
        template.chatComponentType = 'EVENT';
        template.customData.msg = '';
        return template;
    }
    if (msgItem.c === 'Event') {
        template.chatComponentType = 'EVENT';
        template.customData.msg = msgItem.m.body;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'x-filetransfer/octet-stream') {
        template.chatComponentType = 'DOCUMENT';
        template.plainMsg.meta = msgItem.m.meta;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'application/video') {
        template.chatComponentType = 'DOCUMENT';
        template.plainMsg.meta = msgItem.m.meta;
        try {
            template.customData.filename = msgItem.m.meta.filename;
            template.customData.width = msgItem.m.meta.w;
            template.customData.height = msgItem.m.meta.h;
            template.customData.url = msgItem.m.meta.download.url;

            addFileProcessFileds(template.plainMsg);
        } catch (e) {
            console.error('[error]: ', 'message error', msgItem);
        }
        return template;
    }
    // sticker need fetch from market
    if (msgItem.m.MIMETYPE === 'application/sticker') {
        template.chatComponentType = 'STICKER';
        delete template.customData.msg;
        template.customData.width = msgItem.m.meta.w;
        template.customData.height = msgItem.m.meta.h;
        template.customData.filename = msgItem.m.meta.filename;
    }
    if (msgItem.m.MIMETYPE === 'application/announcement') {
        template.chatComponentType = 'Announcement';
        template.customData.msg = msgItem.m.meta?.announcementInfo?.content;
        template.plainMsg.body = msgItem.m.meta?.announcementInfo?.content;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'robot/richtext') {
        template.chatComponentType = 'RobotRich';
        template.customData.msg = msgItem.m.meta.title;
        template.plainMsg.body = msgItem.m.meta.title;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'text/combine') {
        template.chatComponentType = 'CombineText';
        // template.customData.msg = msgItem.m.meta.title;
        // template.plainMsg.body = msgItem.m.meta.title;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'forward/robot/richtext') {
        template.chatComponentType = 'ForRobotRich';
        template.customData.msg = msgItem.m.meta.title;
        template.plainMsg.body = msgItem.m.meta.title;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'robot/text') {
        template.chatComponentType = 'MESSAGE';
        const newMessage = `${msgItem.m.meta.title}\n\n${msgItem.m.meta.text}`;
        template.customData.msg = newMessage;
        template.plainMsg.body = newMessage;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'edit/robot/text') {
        template.chatComponentType = 'MESSAGE';
        const newMessage = `${msgItem.m.meta.title}\n\n${msgItem.m.meta.text}`;
        template.customData.msg = newMessage;
        template.plainMsg.body = newMessage;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'edit/robot/richtext') {
        template.chatComponentType = 'RobotRich';
        const newMessage = `${msgItem.m.meta.title}\n\n${msgItem.m.meta.text}`;
        template.customData.msg = newMessage;
        template.plainMsg.body = newMessage;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'forward/robot/text') {
        template.chatComponentType = 'MESSAGE';
        const newMessage = `${msgItem.m.meta.title}\n\n${msgItem.m.meta.text}`;
        template.customData.msg = newMessage;
        template.plainMsg.body = newMessage;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'longtext/plain') {
        template.chatComponentType = 'MESSAGELONG';
        template.customData.msg = msgItem.m.body;
        return template;
    }
    if (
        msgItem.m.MIMETYPE === 'image/webp' ||
        msgItem.m.MIMETYPE === 'image/png' ||
        msgItem.m.MIMETYPE === 'image/jpeg' ||
        msgItem.m.MIMETYPE === 'image/jpg' ||
        msgItem.m.MIMETYPE === 'image/bmp' ||
        msgItem.m.MIMETYPE === 'image/gif' ||
        msgItem.m.MIMETYPE === 'application/sticker'
    ) {
        template.chatComponentType = 'PICTURE';
        delete template.customData.msg;
        try {
            template.customData.filename = msgItem.m.meta.filename;
            template.customData.width = msgItem.m.meta.w;
            template.customData.height = msgItem.m.meta.h;
            template.customData.url = msgItem.m.meta.download.url;
        } catch (e) {
            console.error('[error]: ', 'message error', msgItem);
        }
        return template;
    }

    // if (msgItem.m.MIMETYPE === 'application/video') {
    //     template.chatComponentType = 'VIDEO';
    //     template.plainMsg.meta = msgItem.m.meta;
    //     return template;
    // }

    // 受限的类型
    if (msgItem.m.MIMETYPE === 'application/restricted') {
        let data;
        try {
            let msgMeta;
            if (msgItem.m?.meta && typeof msgItem.m?.meta === 'string') {
                msgMeta = JSON.parse(msgItem.m.meta);
            }
            if (msgItem.m?.meta && typeof msgItem.m?.meta === 'object') {
                msgMeta = msgItem.m?.meta;
            }
            data = msgMeta ? msgRestrictedTypeInfo[msgMeta.restrictedType] : '';
        } catch (e) {
            console.error('[error]: ', 'message error', e, msgItem);
        }
        template.customData.msg = data ? `[${data}]` : '[Restricted message]';
        template.chatComponentType = 'RESTRICTED';
        return template;
    }

    if (msgItem.m.MIMETYPE === 'richtext/plain') {
        template.chatComponentType = 'RICHTEXT';
        return template;
    }

    if (msgItem.m.MIMETYPE === 'application/card') {
        template.chatComponentType = 'UNSUPPORTED';
        template.customData.msg = `[${i18n.t('UnsupportedThisDevice')}]`;
        return template;
    }
    if (msgItem.m.MIMETYPE === 'text/plain') {
        return template;
    } else {
        template.chatComponentType = 'UNKNOW';
        try {
            if (msgItem.isMine) {
                template.customData.msg = `[You've sent a ${
                    msgTypeInfo[msgItem.m.MIMETYPE] || 'incompatible message'
                }, please check on a mobile device]`;
            } else {
                if (msgItem.m.MIMETYPE === 'audio/voice-msg') {
                    template.customData.msg = `[You've received a ${
                        msgTypeInfo[msgItem.m.MIMETYPE] || 'incompatible message'
                    }, please check on a mobile device]`;
                } else {
                    template.customData.msg = `[${i18n.t('receivedNewTypeMsg')}]`;
                }
            }
        } catch (e) {
            template.customData.msg = `[${i18n.t('receivedNewTypeMsg')}]`;
        }
    }

    return template;
}
