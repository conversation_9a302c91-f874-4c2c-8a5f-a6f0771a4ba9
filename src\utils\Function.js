import store from '@/store/index';
import {fetchWholeForContact} from '@/api/peerApi';
import {toUploadLog} from '@/api/uploadApi';
import {getHid} from '@/dataController/hid';
import {getWith} from '@/utils/SqliteUtil';
import {getPFMBaseURL} from '@/utils/base';
import {MsgTemplate, handleMessage} from '@/api/messageManger.js';
import {dataUtil} from '@/utils/dataUtil';
import * as FS from 'fs';
import * as crypto from 'crypto';
import {commonUCRequest} from '@/api/axiosInstance';
import path from 'path';
import {configDir} from '@/config/config.js';
import countries from './countries.json';
// called autollcy when open app
import {e2eDataUtil} from '@/utils/e2eUtil.js';
import {mangePeerList} from '@/utils/doPolymericPeer';
import {getSpaceId} from '@/utils/SpaceManager';
import {defaultSpaceId} from '@/dataController/hid';
import moment from 'moment';
import fse from 'fs-extra';
import {version} from '../../package.json';
const compressing = require('compressing');
import {MessageBox} from 'element-ui';
import {queryPreference} from '@/api/userInfoApi';
const Message = window.$message;
import devLog from '@/logs/devLog';

import {ipcRenderer} from 'electron';
import {getTimestamp} from '@/utils/timestampFix';
import {enCodeSpaceHid} from '@/dataController/hid';
import {msgRestrictedTypeInfo} from '@/utils/renderMsgItem';
import {PlainException} from './../utils/caughtError';
import envConfig from '@/config/config';
import {encryptFile} from '@/tools/upload/encryptFile.js';
import {renderName} from '@/utils/index';
import i18n from '@/lang/index';
import {warpMessageDestroyInterval} from '@/utils/message/destroyMsg/sendDestroyMsg.js';
import {matchMeetingUrl} from '@/utils/meeting';
import {joinMeetingEnum} from '@/utils/report/reportUtil';
import MeetingService from '@/utils/meeting/meetingService.js';
import Bus from '@/utils/vueBus';

import {getGroupAvatar} from '@/utils/avatar/index';
import {handleGetRemoteStoragePath} from '@/utils/downloadSetting.js';
import {sendFileMsg} from '@/utils/message/sendMsg.js';
import {parseMarkdownToForwardContent} from '@/utils/editor/parse.js';
import {v1 as uuidv1} from 'uuid';

// 二次启动时在pushreg也会刷新通讯录合群组，重复了
function initial() {
    let inited = false;
    return async function () {
        if (inited) {
            void null;
        } else {
            inited = true;
            // store.dispatch("concat/refreshContacts",{
            //   spaceId:defaultSpaceId(),
            //   force: true,
            // })
            // console.log('refreshContacts----7')
            // store.dispatch("concat/refreshChannels",{
            //   spaceId:defaultSpaceId()
            // })
            // store.dispatch('setting/refresh', {
            //     type: 'initial'
            // });
            getInfo();
            initLogKey();
        }
    };
}

export function startQueryPreference() {
    console.log('startQueryPreference');
    return queryPreference({
        type: 'ReadReceiptStatus,RemoteStoragePath'
    })
        .then(res => {
            store.dispatch('setting/setReceipt', Boolean(res.ReadReceiptStatus));
            handleGetRemoteStoragePath(res.RemoteStoragePath);
        })
        .catch(err => {
            console.log('queryPreference err', err);
        });
}

async function filterCstLog(dirPath, dist, isFeedback = false) {
    console.log('filterCstLog', dirPath, dist, isFeedback);
    try {
        // if (!isFeedback) {
        const dirname = dirPath.split('logs')[1];
        // console.log('dirname0', dirname);
        const distFile = path.join(dist, dirname);
        // console.log('distFile: ', distFile);
        await fse.copy(dirPath, distFile);
        // }
        return;

        // const files = FS.readdirSync(dirPath);
        // let last = moment().subtract(2, 'days').valueOf(); //2天前

        // for (let index = 0; index < files.length; index++) {
        //     const ele = files[index];
        //     const dir = path.join(dirPath, ele);
        //     const info = FS.statSync(dir);

        //     if (info.isDirectory()) {
        //         // console.log('dir ', dir, dist)
        //         filterCstLog(dir, dist, isFeedback);
        //     } else {
        //         const dirname = path.dirname(dir).split('logs')[1];
        //         // console.log('dirname0', dirname)
        //         const distFile = path.join(dist, dirname, ele);
        //         // console.log('distFile000', distFile)

        //         if (ele?.includes('.xlog')) {
        //             // console.log('file', file)
        //             if (last <= info.birthtime || !isFeedback) {
        //                 await fse.copy(dir, distFile);
        //             }
        //         } else {
        //             await fse.copy(dir, distFile);
        //         }
        //     }
        // }
    } catch (error) {
        console.error('file catch', error);
    }
}

async function copyCurrentLog(dirPath, dist, uploadType) {
    try {
        const files = FS.readdirSync(dirPath);

        // console.log('copyCurrentLog uploadType', uploadType)
        // console.log('copyCurrentLog files', files)
        for (let index = 0; index < files.length; index++) {
            const ele = files[index];
            const dir = path.join(dirPath, ele);
            const info = FS.statSync(dir);
            if (info.isDirectory()) {
                // console.log('dir ', dir, dist)
                const basename = path.basename(dir);
                // console.log('basename', basename)
                const distFile = path.join(dist, basename);
                // console.log('distFile isDirectory', distFile)

                if (uploadType?.isFeedback) {
                    if (distFile.includes('MeetingSdk')) {
                        if (!uploadType?.isCrystalSdk) {
                            await fse.copy(dir, distFile);
                        }
                    } else {
                        if (uploadType?.isCrystalSdk) {
                            await filterCstLog(dir, dist, true);
                        }
                    }
                } else {
                    if (distFile.includes('MeetingSdk')) {
                        if (getTimestamp() - appdataStorage.getItem('hwMeetingJoinTimestamp') <= 3 * 24 * 60 * 60 * 1000) {
                            await fse.copy(dir, distFile);
                        }
                    } else {
                        await filterCstLog(dir, dist);
                    }
                }
            } else {
                // console.log('curFile', dir)
                const basename = path.basename(dir);
                const dirname = path.dirname(dir).split('logs')[1];
                // console.log('distFile', basename)
                // console.log('dirname', dirname)
                const distFile = path.join(dist, dirname, basename);

                if (basename.includes('.log') && !basename.endsWith('old.log')) {
                    // console.log('distFile', distFile)
                    await fse.copy(dir, distFile);
                }
            }
        }
    } catch (e) {
        console.error('[error]: ', 'copyCurrentLog error', e);
    }
}

async function copyElectronLog(dirPath, dist, uploadType) {
    try {
        const files = FS.readdirSync(dirPath);

        // console.log('copyCurrentLog files', files, uploadType)
        for (let index = 0; index < files.length; index++) {
            const ele = files[index];
            const dir = path.join(dirPath, ele);
            const info = FS.statSync(dir);
            if (!info.isDirectory()) {
                // console.log('curFile', dir)
                const basename = path.basename(dir);
                const dirname = path.dirname(dir).split('logs')[1];
                // console.log('distFile', basename)
                // console.log('dirname', dirname)
                const distFile = path.join(dist, dirname, basename);

                if (basename.includes('.log') && !basename.endsWith('old.log')) {
                    // console.log('distFile', distFile)
                    await fse.copy(dir, distFile);
                }
            } else {
                if (dir.includes('MeetingSdk')) {
                    if (getTimestamp() - appdataStorage.getItem('hwMeetingJoinTimestamp') <= 1 * 24 * 60 * 60 * 1000) {
                        await fse.copy(dir, dist);
                    }
                } else {
                    await filterCstLog(dir, dist, uploadType?.isFeedback);
                }
            }
        }
    } catch (e) {
        console.error('[error]: ', 'copyCurrentLog error', e);
    }
}

async function getLocalDmp(dist) {
    try {
        // let dmpPath = path.join(configDir, '../../Local/Temp', `${pkgName} Crashes/reports`);
        let newdmpPath = path.join(configDir, 'Crashpad');

        // if (FS.existsSync(dmpPath)) {
        //     // console.log('dmpPath', dmpPath)
        //     let files = FS.readdirSync(dmpPath);
        //     // console.log('files', files)
        //     let last = moment().subtract(2, 'days').valueOf(); //2天前
        //     for (let index = 0; index < files.length; index++) {
        //         if (files[index]?.includes('dmp')) {
        //             const file = path.join(dmpPath, files[index]);
        //             const stats = FS.statSync(file);
        //             // console.log('file', file)
        //             if (last < stats.birthtime) {
        //                 await fse.copy(file, path.join(dist, files[index]));
        //             }
        //         }
        //     }
        // }
        if (FS.existsSync(newdmpPath)) {
            // console.log('newdmpPath', newdmpPath)
            let files = FS.readdirSync(newdmpPath);
            // console.log('files', files)
            let last = moment().subtract(2, 'days').valueOf(); //2天前
            for (let index = 0; index < files.length; index++) {
                if (files[index]?.includes('dmp')) {
                    const file = path.join(newdmpPath, files[index]);
                    const stats = FS.statSync(file);
                    // console.log('file', file)
                    if (last < stats.birthtime) {
                        await fse.copy(file, path.join(dist, files[index]));
                    }
                }
            }
        }
    } catch (e) {
        console.log('getLocalDmp err !', e);
    }
}

export async function getLogsArchiver(dirPath, uploadType) {
    return new Promise(async (resolve, reject) => {
        let fileName = `logs-${moment().format('YYYY-MM-DD-hh-mm-ss')}.zip`;
        let distPath = 'upload';
        let dist = path.join(configDir, distPath);
        let compassPath = path.join(configDir, fileName);

        await fse.emptyDir(dist);
        // await fse.copy(dirPath, dist);
        await copyCurrentLog(dirPath, dist, uploadType);
        await getLocalDmp(dist);
        // console.log('dist', dist)
        console.log('compassPath', compassPath);

        compressing.zip
            .compressDir(dist, compassPath)
            .then(async function () {
                console.log('Compression complete');
                const gzipPath = await encryptFile(compassPath).catch(e => {
                    console.error('encryptFile error', e);
                });

                const respath = gzipPath || compassPath;

                resolve({
                    path: respath,
                    name: path.basename(respath)
                });
            })
            .catch(async function (err) {
                console.error('[error]: ', 'Compression error', err);
                reject(err);
            });
    });
}
async function uploadFile_(file) {
    let a = FS.readFileSync(file.path);
    let hash = crypto.createHash('sha256');
    hash.update(a);
    // let hmac = crypto.createHmac("hmac-sha256","")
    // hmac.update(a)
    let params = {
        filename: file.name,
        quality: 1,
        quickexpire: 0,
        sha256: hash.digest().toString('hex'),
        hmac: 'fake-hmac'
    };
    let fileResult = await commonUCRequest({
        timeout: 60 * 1000,
        baseURL: getPFMBaseURL(),
        url: `/pfm/log/send`,
        method: 'POST',
        params: params,
        data: a.buffer,
        headers: {
            'Content-Type': 'application/octet-stream'
        }
    });
    if (fileResult && fileResult.download) {
        return {
            fid: fileResult.download.fid,
            name: file.name
        };
    } else {
        return {
            fid: 'fail',
            name: file.name
        };
    }
}

export function clearTempZip(url) {
    try {
        if (url.indexOf('gz') !== -1 && FS.existsSync(url)) {
            fse.remove(url);
            const zip = url.replace('.gz', '');
            if (FS.existsSync(zip)) {
                fse.remove(zip);
            }
        } else if (url.indexOf('zip') !== -1 && FS.existsSync(url)) {
            fse.remove(url);
            const gzip = url + '.gz';
            if (FS.existsSync(gzip)) {
                fse.remove(gzip);
            }
        }
    } catch (err) {
        console.log('Temp err !', err);
    }
}

export async function uploadLog(isDir, uploadType = {}) {
    return new Promise((resolve, reject) => {
        if (isDir) {
            let logsDir = path.join(configDir, 'logs');
            // let hwlogsDir = path.join(configDir,'../HwmSdk/log');
            // let cstlogsDir = path.join(configDir,'../CrystalSDK');
            // console.log('logsDir', logsDir)
            // console.log('hwlogsDir', hwlogsDir)
            getLogsArchiver(logsDir, uploadType).then(toUploadLog).then(resolve).catch();
            // ]).then(uploadFile_).then(resolve).catch();
        } else {
            ipcRenderer.send('getLogFile');
            ipcRenderer.on('gotLogFile', (e, file) => {
                uploadFile_(file)
                    .then(a => resolve(a))
                    .catch(reject);
            });
        }
    });
}
// 未登录日志上传
export async function unLoginUploadLog(uploadType) {
    return new Promise(async (resolve, reject) => {
        let logsDir = path.join(configDir, 'logs');

        console.log('logsDir', logsDir);
        let distPath = 'upload';
        let dist = path.join(configDir, distPath);

        await fse.emptyDir(dist);
        // await fse.copy(dirPath, dist);
        await copyElectronLog(logsDir, dist, uploadType);
        getLogsFile(dist)
            .then(file => {
                return toUploadLog(file, false);
            })
            .then(resolve)
            .catch();
    });
}
// 压缩单个文件
export async function getLogsFile(dirPath) {
    return new Promise(async (resolve, reject) => {
        let fileName = `logs-${moment().format('YYYY-MM-DD-hh-mm-ss')}.zip`;
        let compassPath = path.join(configDir, fileName);
        console.log('compassPath', compassPath);
        compressing.zip
            .compressDir(dirPath, compassPath)
            .then(async function () {
                console.log('Compression complete');
                const gzipPath = await encryptFile(compassPath).catch(e => {
                    console.error('encryptFile error', e);
                });

                const respath = gzipPath || compassPath;

                resolve({
                    path: respath,
                    name: path.basename(respath)
                });
            })
            .catch(async function (err) {
                console.error('[error]: ', 'Compression error', err);
                reject(err);
            });
    });
}

async function initLogKey() {
    let key = await getHid();
    ipcRenderer.send('initKey', key);
}

export function info(...arg) {
    devLog.log(...arg);
}

async function getInfo() {
    let hid = await getHid();
    let spaceId = defaultSpaceId();
    await fetchWholeForContact(hid, spaceId);
}
export async function getUserInfo(hid) {
    let spaceId = defaultSpaceId();
    const rs = await mangePeerList(spaceId, [hid], 0);
    return rs[0];
}

export async function getCardUserInfo(message) {
    let hid = paths(message, 'm', 'meta', 'contactUid');
    const body = message.body;
    if (!hid && body) {
        const colonIndex = body.lastIndexOf(':');
        const equalIndex = body.lastIndexOf('=');
        hid = body.substring(colonIndex + 1, equalIndex);
    } else {
        hid = dataUtil.numberToHid(hid);
    }
    return getUserInfo(hid);
}

export const initInfo = initial();
export function all(...tests) {
    return tests.reduce((r, a) => r && a, true);
}
// Behind viewport top
export function isBehindViewportTop(detected, view, deviation = 5) {
    let rectIn = detected.getBoundingClientRect();
    let rectOut = view.getBoundingClientRect();
    if (rectIn.top + deviation > rectOut.top) {
        return true;
    } else {
        return false;
    }
}
export function isBeforeViewportBottom(detected, view, deviation = 5) {
    let rectIn = detected.getBoundingClientRect();
    let rectOut = view.getBoundingClientRect();
    if (rectIn.bottom + deviation < rectOut.bottom) {
        return true;
    } else {
        return false;
    }
}

// Inside viewport
export function isInOfViewport(detected, view, deviation = 5) {
    let rectIn = detected.getBoundingClientRect();
    let rectOut = view.getBoundingClientRect();
    if (
        all(
            rectIn.top + deviation > rectOut.top,
            rectIn.bottom - deviation < rectOut.bottom,
            rectIn.left + deviation > rectOut.left,
            rectIn.right - deviation < rectOut.right
        ) ||
        all(rectIn.top < rectOut.top, rectIn.bottom + deviation > rectOut.bottom)
    ) {
        return true;
    } else {
        return false;
    }
}

export function paths(obj, ...keys) {
    return keys.reduce((obj, key) => (obj == null ? null : obj[key]), obj);
}

export function isString(obj) {
    return Object.prototype.toString.call(obj) === '[object String]';
}
export function isObject(obj) {
    return Object.prototype.toString.call(obj) === '[object Object]';
}

/*
all
actDialogId:"AAAAuvTOo94"
1: "{"c":"HyperText","f":"AAAAuvTOo94","t":"ACKHMozi43w","s":"AVkI33-5lkI","l":5184000,"a":5,"expire":1601554214819,"needAck":true,"meTo":"9718800389956476#web","meFrom":"97118622978774594#mobile","traceId":"885e3f4f-1a96-47d9-b80d-e03643e82a2a","m":{"MIMETYPE":"text/plain","meta":{"ref":[{"hid":"AAAAuvTOo94=","len":3,"start":1}]},"nf":1,"ctime":1596370213333,"stime":1596370214818,"receipt":1,"body":"@All","uuid":"11bcd755-59d2-41e6-8c38-7fd644e205b8|GACKHMozi43w"}}"

origin
"safsdf @<a ccd="ccd" hid="DXp90Pz1tq0" contenteditable="false">Leo1111 Xiang i1111</a> end"

encodeForMetion(`safsdf @<a ccd="ccd" hid="DXp90Pz1tq0" contenteditable="false">Leo1111 Xiang i1111</a> end @<a hid="22">22</a>end`)
*/
export function encodeForMetion(str) {
    if (str && str != '') {
        let body = '';
        let {i, prefix, tail} = splitOnce(str, '<a metion');
        if (i == -1) {
            return {
                body: str,
                ref: []
            };
        } else {
            body += prefix;
            let {tail: hidStart} = splitOnce(tail, 'hid="');
            let {prefix: hid, tail: token} = splitOnce(hidStart, '"');
            let {tail: metionStart} = splitOnce(token, '>');
            let {tail: token2} = splitOnce(metionStart, '</a>');

            let {tail: nameStart} = splitOnce(token, 'name="');
            let {prefix: name} = splitOnce(nameStart, '"');

            body += '@' + name;
            let refs = [
                {
                    len: name.length,
                    start: i + 1,
                    hid
                }
            ];

            let tailResult = encodeForMetion(token2);
            refs.push(
                ...tailResult.ref.map(a => {
                    a.start += body.length;
                    return a;
                })
            );
            body += tailResult.body;
            return {
                body,
                ref: refs
            };
        }
    } else {
        return {
            body: '',
            ref: []
        };
    }
}

export function translateMsgMetionToAlias({body, ref, dialogId}) {
    if (!body) {
        return '';
    }
    if (!ref || !ref?.length) {
        return body;
    }

    let translateMsg = body;
    let lastIndex = 0;
    let result = [];

    try {
        ref.forEach(item => {
            const hid = item.hid.replace(/=/g, '');
            result.push(translateMsg.slice(lastIndex, item.start - 1));
            if (dialogId !== hid) {
                const peer = store.state.peerCollection[enCodeSpaceHid(hid, defaultSpaceId())];

                let isGroup = dataUtil.getPeerType(hid) == 'group';

                peer && result.push(isGroup ? i18n.t('im_toall') : `@${renderName(peer, 2)}`);
            } else {
                result.push(translateMsg.slice(item.start - 1, item.start + item.len));
            }
            lastIndex = item.start + item.len;
        });
        result.push(translateMsg.slice(lastIndex, translateMsg.length));
        return result.join('');
    } catch (error) {
        console.error('🚀 ~ file: Function.js:557 ~ translateMsgMetionToAlias ~ error', error);
        return body;
    }
}

function splitOnce(str, betarget) {
    let i = str.indexOf(betarget);
    if (i == -1) {
        return {
            i,
            prefix: str,
            tail: ''
        };
    } else {
        return {
            i,
            prefix: str.slice(0, i),
            tail: str.slice(i + betarget.length)
        };
    }
}

//   remove equal sign
export function removeEqualSign(a) {
    if (a == null) {
        return '';
    } else {
        return a.replace(/=/g, '');
    }
}
export function forceAddEqForHid(hid) {
    if (hid == 'all') {
        return hid;
    } else if (hid?.indexOf('=') == -1) {
        return hid + '=';
    } else {
        return hid;
    }
}

export function timeLogic(currentMsg, lastMsg) {
    const CHAT_TIME_INTERVAL = 1000 * 60 * 5;
    if (currentMsg.peerId != lastMsg.peerId) {
        currentMsg.isShowTimeStamp = true;
        // 创建群组第一条消息需要有头像
    } else if (
        (lastMsg && lastMsg.plainMsg && lastMsg.plainMsg.MIMETYPE) == 'application/withdraw' ||
        (lastMsg && lastMsg.plainMsg && lastMsg.plainMsg.m && lastMsg.plainMsg.m.name) == 'GroupCreate'
    ) {
        currentMsg.isShowTimeStamp = true;
    } else if (paths(lastMsg, 'plainMsg', 'c') == 'E2EKeyChange') {
        currentMsg.isShowTimeStamp = true;
    } else if (paths(lastMsg, 'chatComponentType') == 'EVENT') {
        currentMsg.isShowTimeStamp = true;
    } else if (paths(lastMsg, 'chatComponentType') == 'RobotRich') {
        currentMsg.isShowTimeStamp = true;
    } else if (currentMsg.unreadMessage == true) {
        currentMsg.isShowTimeStamp = true;
    } else if (currentMsg.stime - (lastMsg && lastMsg.stime) > CHAT_TIME_INTERVAL) {
        currentMsg.isShowTimeStamp = true;
    } else if (
        lastMsg &&
        moment(new Date(currentMsg.stime)).format('YYYY-MM-DD') !== moment(new Date(lastMsg.stime)).format('YYYY-MM-DD')
    ) {
        currentMsg.isShowTimeStamp = true;
    } else {
        currentMsg.isShowTimeStamp = false;
    }
}
export function dateLogic(currentMsg, lastMsg) {
    const currDate = currentMsg.stime <= 0 ? 0 : moment(new Date(currentMsg.stime)).format('YYYY-MM-DD');
    const lastDate = lastMsg.stime <= 0 ? 0 : moment(new Date(lastMsg.stime)).format('YYYY-MM-DD');
    if (currDate === lastDate) {
        currentMsg.isShowDate = false;
    } else {
        currentMsg.isShowDate = true;
    }
}
export const order = by => propLens => a => b => {
    if (propLens.length == 0) {
        return 0;
    } else {
        let [head, ...tail] = propLens;
        let aVal = head(a);
        let bVal = head(b);
        if (aVal == bVal) {
            return order(tail)(a)(b);
        } else {
            return by(aVal, bVal) ? 1 : -1;
        }
    }
};
export const sortBy = xs => fn => {
    if (!Array.isArray(xs)) {
        console.error('[error]: ', 'sortBy xs', xs);
    }
    return xs.sort((a, b) => {
        let r = fn(a)(b);
        return r;
    });
};
export const dot =
    (...fns) =>
    a => {
        return fns.reduce((param, fn) => fn(param), a);
    };
export const toLowerCase = str => str && str.toLowerCase();
export const prop = name => obj => {
    return obj[name] ? obj[name] : `${obj.firstName || ''} ${obj.lastName || ''}`;
};
export const firstName = dot(prop('firstName'), toLowerCase);
export const lastName = dot(prop('lastName'), toLowerCase);
export const alias = dot(prop('alias'), toLowerCase);
export const name = dot(prop('name'), toLowerCase);
export const ascending = order((a, b) => a > b);
export const descending = order((a, b) => a < b);

export const forwardFile = filePath => hids => sendFileMsg(filePath, hids);

export const forwords =
    (uuid, isFromGroupPin = false, groupPinInfo, isNeedAt = false, data) =>
    hids =>
        Promise.all(hids.map(forword(uuid, isFromGroupPin, groupPinInfo, isNeedAt, data)));
export const forwordMerges = messageList => hids => Promise.all(hids.map(forwordMerge(messageList)));
export function isPicture(message) {
    return (
        message.MIMETYPE && ['image/webp', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp'].indexOf(message.MIMETYPE) != -1
    );
}
export function isForRobot(message) {
    return message.MIMETYPE == 'forward/robot/richtext';
}
export function isMeetingInvite(message) {
    return message.MIMETYPE == 'meeting/invite';
}

export function isDocument(message) {
    return message.MIMETYPE == 'x-filetransfer/octet-stream';
}
export function isRestricted(message) {
    return message.MIMETYPE == 'application/restricted';
}
export function isVcard(message) {
    return message.MIMETYPE == 'text/vcard';
}
export function isVoice(message) {
    return message.MIMETYPE === 'audio/voice-msg';
}
export function isCall(message) {
    return message.MIMETYPE === 'call/record';
}

export function isText(message) {
    return message.MIMETYPE === 'text/plain' && !isE2EE(message);
}
export function isE2EE(message) {
    //isStar 表示失败意思 efailed
    return paths(message, 'isStar');
}
export function isReplyMeetingCard(message) {
    return paths(message, 'replyMeetingCard');
}

export function isWithDraw(message) {
    return message.MIMETYPE === 'application/withdraw' || message.MIMETYPE === 'application/eliminate';
}
export function isPoiCard(message) {
    return message.MIMETYPE === 'poi/card';
}
export function isLocationShare(message) {
    return message.MIMETYPE === 'location/share';
}
export function isForwardApprovalCard(message) {
    return message.MIMETYPE === 'forward/approval/card';
}
export function isMessageCard(message) {
    return message.MIMETYPE === 'edit/application/card';
}
export function isVideo(message) {
    return message.MIMETYPE === 'application/video';
}
export function isRichText(message) {
    return message.MIMETYPE === 'richtext/plain';
}
export const createMsg = message => hid => {
    let msg = new MsgTemplate();
    let preMessageM;
    if (message && message.m) {
        if (typeof message.m == 'string') {
            preMessageM = JSON.parse(message.m);
        } else {
            preMessageM = message.m;
        }
    }
    let msga = msg.getMsgTemplate(hid, message.body, []);
    msga.m.stime = getTimestamp();
    msga.m = _.merge(preMessageM, msga.m);
    msg = msg.setList(msga);
    msg.m.MIMETYPE = message.MIMETYPE;
    delete msg.m.isE2EE;

    if (isVoice(message) || isVideo(message)) {
        msg.binaryPart = message.binaryPart;
    }

    return msg;
};

export const createInviteMsg = message => hid => {
    let msg = new MsgTemplate();
    let meetingInfo = JSON.parse(message.meta);
    let msga = msg.getInviteTemplate(hid, meetingInfo);
    msg = msg.setList(msga);
    delete msg.m.isE2EE;
    msg.m.MIMETYPE = message.MIMETYPE;
    return msg;
};

// 参考RightcontentPanel里面的转发代码
export async function createFileMessage(message, hid) {
    let meta = {};
    try {
        if (Object.prototype.toString.call(message && message.meta) == '[object String]') {
            meta = JSON.parse(message.meta);
        } else {
            meta = (message && message.m && message.m.meta) || {};
        }
    } catch (e) {
        console.error('createFileMessage', e);
    }
    meta.progress = meta.progress || {};
    let file = {
        type: message.MIMETYPE,
        name: meta.filename,
        path: meta.progress.uploadOriginFilePath
    };
    let template = new MsgTemplate();
    let msgBody = await template.getFileTemplate(hid, file);
    msgBody.m.meta = {
        isOrigin: 0,
        iKey: meta.iKey,
        hmacKey: meta.hmacKey,
        download: meta.download,
        filename: meta.filename,
        originSha256: meta.originSha256
    };
    msgBody.m.MIMETYPE = message.MIMETYPE;
    if (msgBody.m.MIMETYPE != 'x-filetransfer/octet-stream') {
        msgBody.m.meta.download = meta.download;
        msgBody.m.meta.w = meta.w;
        msgBody.m.meta.h = meta.h;
        msgBody.binaryPart = message.binaryPart;
    }
    let param = template.setList(msgBody);
    return param;
}
// 转发（needRepliedInfo：表示是否加上回复消息）
export const forword =
    (uuid, isFromGroupPin = false, groupPinInfo, isNeedAt = false, data) =>
    async (hid, needRepliedInfo) => {
        let message = await getWith('select * from message where uuid = :uuid', {uuid}, defaultSpaceId());

        if (isFromGroupPin && groupPinInfo && !message) {
            message = groupPinInfo;
        }
        if (message && message.MIMETYPE == 'application/withdraw') {
            Message({
                type: 'warning',
                message: i18n.t('message_delete_sorry')
            });
            throw i18n.t('message_delete_sorry');
        }
        let msg;
        if (data) {
            msg = new MsgTemplate().getMsgTemplate(hid);

            msg.m.stime = getTimestamp();

            msg.m = _.merge(msg.m, data.m);

            if (data.binaryPart) {
                msg.binaryPart = data.binaryPart;
            }
        } else if (isDocument(message)) {
            msg = await createFileMessage(message, hid);
        } else if (isPicture(message)) {
            msg = await createFileMessage(message, hid);
            if (message.m) {
                let metaJSON = JSON.parse(message.m);
                msg.m.meta = metaJSON && metaJSON.meta;
                msg.m.meta.progress = {
                    // 已下载size
                    loaded: 0,
                    // 总大小
                    total: msg.m.meta.download.size,
                    // 是否未被取消
                    enabled: true,
                    type: 'download',
                    sendStatus: ''
                };
            }
        } else if (isVideo(message)) {
            msg = await createFileMessage(message, hid);
            msg.binaryPart = message.binaryPart;
            if (message.m) {
                let metaJSON = JSON.parse(message.m);
                msg.m.meta = metaJSON && metaJSON.meta;
                msg.m.meta.progress = {
                    // 已下载size
                    loaded: 0,
                    // 总大小
                    total: msg.m.meta.download.size,
                    // 是否未被取消
                    enabled: true,
                    type: 'download',
                    sendStatus: ''
                };
            }
        } else if (isMeetingInvite(message)) {
            let createMsgFor = createInviteMsg(message);
            msg = createMsgFor(hid);
        } else {
            let createMsgFor = createMsg(message);
            msg = createMsgFor(hid);
        }
        if (!needRepliedInfo && paths(msg, 'm', 'meta', 'repliedInfo')) {
            delete msg.m.meta.repliedInfo;
        }

        // 转发把受限的消息类型改为文本类型
        if (msg.m.MIMETYPE === 'application/restricted') {
            msg.m.MIMETYPE = 'text/plain';
            try {
                const restrictedType = msg.m?.meta?.restrictedType;
                let data;
                if (restrictedType) {
                    data = msgRestrictedTypeInfo[restrictedType];
                }
                if (data) {
                    msg.m.body = `[${data}]`;
                }
            } catch (error) {
                console.error('[error :>> ]', error);
            }
        }
        let spaceId = getSpaceId(message.mcTo);
        if (msg.m.MIMETYPE === 'robot/richtext') {
            msg.m.MIMETYPE = 'forward/' + msg.m.MIMETYPE;
            msg.m.meta.robotId = dataUtil.hidToNumber(message.f);
            await warpMessageDestroyInterval(msg, spaceId);
        }
        if (msg.m.MIMETYPE === 'robot/text') {
            msg.m.MIMETYPE = 'text/plain';
            msg.m.body = msg.m.meta.title + '\n\n' + msg.m.meta.text;
            msg.m.meta = {};
            await warpMessageDestroyInterval(msg, spaceId);
        }

        if (msg.m.MIMETYPE === 'richtext/plain') {
            msg.m.body = parseMarkdownToForwardContent(msg.m.body);
        }

        // 转发不需要带上groupPin信息
        if (paths(msg, 'm', 'meta', 'pinnedInfo') || msg.pinnedInfo) {
            if (msg.content && msg.content?.m?.meta && msg.content?.m?.meta?.pinnedInfo) {
                msg.content.m.meta.pinnedInfo = null;
            }
            if (msg.m.meta && msg.m.meta.pinnedInfo) {
                msg.m.meta.pinnedInfo = null;
            }
            if (msg.meta && msg.meta.pinnedInfo) {
                msg.meta.pinnedInfo = null;
            }
        }

        if (paths(msg, 'm', 'meta', 'editInfo') || msg.editInfo) {
            if (msg.content && msg.content?.m?.meta && msg.content?.m?.meta?.editInfo) {
                msg.content.m.meta.editInfo = null;
            }
            if (msg.m.meta && msg.m.meta.editInfo) {
                msg.m.meta.editInfo = null;
            }
            if (msg.meta && msg.meta.editInfo) {
                msg.meta.editInfo = null;
            }
        }

        msg.fromLocal = true;
        // 删除历史消息标志
        if (paths(msg, 'm', 'reqId')) {
            msg.m.reqId = '';
        }

        // 删除历史消息标志
        if (paths(msg, 'm', 'meta', 'announcementInfo')) {
            delete msg.m.meta.announcementInfo;
        }

        // 转发消息的时候，把表情列表清空
        const msgMMeta = paths(msg, 'm', 'meta');
        if (msgMMeta && typeof msgMMeta === 'object') {
            msg.m.meta.stickerRepliedList = [];

            // 转发消息 把@xxx 高亮去掉
            // 重发消息 不需要去掉@
            if (!isNeedAt) {
                msg.m.meta.ref = null;
            }
        }

        // 标识该消息是自己转发出去的消息
        msg.isMine = true;

        delete msg.m?.lastSeq;
        delete msg.m?.msgSeq;
        delete msg.m?.meta?.preview;
        delete msg.m?.meta?.richTextRef;
        delete msg.m?.meta?.translateStatus;

        if (msg.m?.meta?.medias?.length) {
            msg.m?.meta?.medias?.forEach(item => {
                delete item?.assertPath;
            });
        }

        if (msg.m?.receipt) delete msg.m.receipt;
        if (msg.m.meta?.receiptInfos) delete msg.m.meta.receiptInfos;
        if (msg.m.meta?.receiptInfo) delete msg.m.meta.receiptInfo;
        // console.log('gotMembersToBeForward', msg.m);
        // 转发图片过多时会出现超时现象，被误认为失败
        // 故设置，timeout 90秒超时
        return sendMsg(msg);
    };
async function sendE2EMessage(sendData, cmd, ws) {
    let spaceId = getSpaceId(sendData.mcTo);
    let currentUserList = await mangePeerList(spaceId, [sendData.t], 0);
    let currentUser = currentUserList && currentUserList[0];
    if (currentUser && currentUser.e2eDeviceMap) {
        const peer_uid_buffer = Buffer.from(sendData.t, 'base64').reverse(); // BE to LE
        const peerUid = peer_uid_buffer.readBigInt64LE().toString();
        let deviceKeyList = Object.keys(currentUser.e2eDeviceMap);
        for (let index = 0; index < deviceKeyList.length; index++) {
            const deviceId = deviceKeyList[index];

            let tempPlayload = _.merge({}, sendData);
            const deviceType = currentUser.e2eDeviceMap[deviceId];
            const meTo = peerUid + '#' + deviceType;
            tempPlayload.meTo = meTo;
            tempPlayload.m.flags = 22;
            tempPlayload.m.isE2EE = true;
            tempPlayload.isSyncFrom = false;
            tempPlayload.m.deviceId = 2; //it is my deviceId
            if (await e2eDataUtil.encodeMessage(tempPlayload, peerUid, deviceId)) {
                ws._sendMsg([tempPlayload], cmd);
            }
        }
        let senderList = await mangePeerList(spaceId, [sendData.f], 0);
        let sender = senderList[0];
        if (sender.e2eDeviceMap) {
            // console.log('[E2EE] send to other device');
            const deviceMap =
                Object.prototype.toString.call(sender.e2eDeviceMap) == '[object String]'
                    ? JSON.parse(sender.e2eDeviceMap)
                    : sender.e2eDeviceMap;
            const uid_buffer = Buffer.from(sendData.f, 'base64').reverse(); // BE to LE
            const uid = uid_buffer.readBigInt64LE().toString();
            // console.log('[end send 1]',JSON.stringify(sendData))
            let deviceKeyList = Object.keys(deviceMap);
            for (let index = 0; index < deviceKeyList.length; index++) {
                const deviceId = deviceKeyList[index];

                let tempPlayload = _.merge({}, sendData);
                const deviceType = deviceMap[deviceId];
                const meTo = uid + '#' + deviceType;
                tempPlayload.meTo = meTo;
                tempPlayload.m.flags = 22;
                tempPlayload.m.isE2EE = true;
                tempPlayload.isSyncFrom = true;
                tempPlayload.m.deviceId = 2; //it is my deviceId

                if (await e2eDataUtil.encodeMessage(tempPlayload, uid, deviceId)) {
                    ws._sendMsg([tempPlayload], cmd);
                }

                // console.log('[end send E2EE-Help-Method 1]',JSON.stringify(tempPlayload))
            }
        }
    } else {
        ws._sendMsg([sendData], cmd);
    }
}

export const sendPlainMsg = cmd => payload => {
    return new Promise(async (resolve, reject) => {
        const ws = store.state.uiControl.socketInstance;
        // 需要等待消息成功
        console.log('sendPlainMsg', payload);
        await handleMessage(payload);
        sendE2EMessage(payload, cmd, ws);
        resolve();
    });
};
export const sendMsg = sendPlainMsg('HyperText');

// Create an atom-queue that runs promise functions order by calling time
export function createAtomRun() {
    let queue = [];
    let run_ = async (number = 1) => {
        if (queue.length == 0) {
            void null;
        } else {
            let a = queue.shift();
            try {
                await a();
                a = null;
            } catch (e) {
                info(() => 'AtomrunError::' + e);
                console.error('[error]: ', 'atomrunerror', e);
            }
            // console.log("run:",number)
            await run_(number + 1);
        }
    };
    let isRuning = false;
    let run = async () => {
        if (isRuning == true) return;
        isRuning = true;
        await run_();
        isRuning = false;
    };
    return async fn => {
        queue.push(fn);
        return run();
    };
}

// Global atom logic
const atomRun = createAtomRun();
export function globalAtomRun(fn) {
    return atomRun(fn);
}

// Consume Command
// produce a command
// consume a command
export function createConsumeCommand() {
    let commandStore = {};
    function produce(id, fn, timeout) {
        commandStore[id] = fn;
        if (timeout) {
            setTimeout(() => {
                if (commandStore[id] == fn) {
                    fn('timeout');
                }
            }, timeout);
        }
    }
    function consume(id, ...args) {
        if (commandStore[id]) {
            let r = commandStore[id](...args);
            if (id.indexOf('forever') == 0) {
                // Do nothing
            } else {
                delete commandStore[id];
            }
            return r;
        } else {
            // Nothing
            return -7;
        }
    }
    return [produce, consume];
}

const globalConsume_ = createConsumeCommand();
export function globalConsume() {
    return globalConsume_;
}

export function isFailLogic(status, info) {
    return (
        (status[info.uuid] || info.plainMsg.messageStatus) == 1 ||
        (info.plainMsg?.MIMETYPE === 'x-filetransfer/octet-stream' && info.plainMsg?.meta?.progress?.sendStatus === 'uploadPause')
    );
}
// 消息发送中
export function isSendingLogic(status, info) {
    return (status[info.uuid] || info.plainMsg.messageStatus) == 2;
}
export function isSuccessLogic(status, info) {
    return Number(status[info.uuid] || paths(info, 'plainMsg', 'messageStatus')) === 3;
}

// draft

let createDraft = store => {
    return (uuid, content) => {
        if (content == null) {
            return store[uuid] || '';
        } else {
            store[uuid] = content;
            return content;
        }
    };
};

export const draft = createDraft({});

export function leftBottomPosotion(el, rectShowed) {
    let viewport = {
        width: window.innerWidth,
        height: window.innerHeight
    };
    const box = Array.from(document.querySelectorAll('#contactCard-box')).find(ele => ele.offsetWidth);
    // console.log('box', box, box?.getBoundingClientRect());
    rectShowed = box?.getBoundingClientRect() || rectShowed;
    let a = el.getBoundingClientRect();
    console.log(a);
    let result = {};
    if (a.bottom + rectShowed.height > viewport.height) {
        result.bottom = '0px';
    } else {
        result.top = a.bottom + 'px';
    }

    if (a.left - rectShowed.width < 0) {
        if (a.right + rectShowed.width > viewport.width) {
            if (i18n.locale === 'ar') {
                result.left = a.left + a.width / 2 - rectShowed.width + 'px';
            } else {
                result.left = a.left + a.width / 2 + 'px';
            }
        } else {
            if (i18n.locale === 'ar') {
                result.left = a.right + 'px';
            } else {
                result.left = a.left + a.width / 2 + 'px';
            }
        }
    } else {
        if (i18n.locale === 'ar') {
            result.left = a.right + 'px';
        } else {
            result.left = a.left - rectShowed.width + 'px';
        }
    }
    return result;
}

export function rightBottomPosition(el, rectShowed) {
    let viewport = {
        width: window.innerWidth,
        height: window.innerHeight
    };
    let a = el.getBoundingClientRect();
    console.log(a);
    let result = {};
    if (a.bottom + rectShowed.height > viewport.height) {
        result.top = viewport.height - rectShowed.height + 'px';
    } else {
        result.top = a.bottom + 'px';
    }
    if (a.right + rectShowed.width > viewport.width) {
        result.left = viewport.width - rectShowed.width + 'px';
        console.log(viewport.width, rectShowed.width);
    } else {
        result.left = a.right + 'px';
        if (i18n.locale === 'ar') {
            result.transform = 'translate(-100%,0)';
            result.left = a.right - a.width + 'px';
            result.right = 'auto';
        }
    }

    return result;
}

export function metionAnimation(el) {
    let message = el && el.querySelector('.message-section');
    if (message) {
        message.classList.add('view-animation');
        let stt = window.setTimeout(() => {
            message.classList.remove('view-animation');
            window.clearTimeout(stt);
        }, 2000);
    }
}

export function pipeGlobalHandle(name, fn) {
    let origin = window[name];
    if (origin) {
        window[name] = function (...args) {
            origin.apply(this, args);
            fn.apply(this, args);
        };
    } else {
        window[name] = fn;
    }
}
export const cname = calcNameForAvatarFromPeer;
export function calcNameForAvatarFromPeer(peer) {
    // console.log('peer', peer?.firstName, peer?.lastName, peer?.hid);
    function firstName(a) {
        if (a) {
            if (a.firstName) {
                return a.firstName;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }
    function lastName(a) {
        if (a) {
            if (a.lastName) {
                return a.lastName;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }
    if (peer == null) {
        return '';
    } else if (dataUtil.getPeerType(peer.hid) === 'group') {
        return peer.name;
    } else if (!peer?.firstName && !peer?.lastName && peer?.name) {
        return peer.name;
    } else {
        return firstName(peer).split(' ').join('') + ' ' + lastName(peer).split(' ').join('');
    }
}
export function isHistoryFromTop(msg) {
    return (paths(msg, 'm', 'reqId') || '').split('<@>')[0] == 'pullDetail-iam-2' || msg?.__isHistoryFromTop;
}

export function ifLoadMoreIsNeeded(dom, vm) {
    if (dom) {
        let topdiv = dom.querySelector('#topdiv');
        let bottomdiv = dom.querySelector('#bottomdiv');
        if (isBehindViewportTop(topdiv, dom, 0)) {
            vm.scrollTopLoadMore_();
        } else {
            // No more message to be loaded
        }
        if (isBeforeViewportBottom(bottomdiv, dom, 0)) {
            vm.scrollTopLoadNew_();
        } else {
            // No more message to be loaded
        }
    } else {
        console.error('[error]: ', 'no topdiv in doms');
    }
}

export function isSimpleEmail(email) {
    if (email == null) {
        return false;
    } else {
        return email.indexOf('@') != -1;
    }
}

/**
 *
code: "AF"
dial_code: "+93"
name: "Afghanistan"

 */
let countriesCache = [];
export function getCountries() {
    if (countriesCache.length == 0) {
        countries.forEach(country => {
            // let found = countriesCache.find(a => a.dial_code == country.dial_code);
            // if (found) {
            //     void null;
            // } else {
            countriesCache.push(country);
            // }
        });
        return countriesCache;
    } else {
        return countriesCache;
    }
}

export function lookupCode(phone) {
    let digitCode = code => code.replace('+', '');
    let countries = getCountries();
    let country = countries.find(({dial_code}) => phone.indexOf(digitCode(dial_code)) == 0);
    let code = country ? country.dial_code : '0';
    let childPhone = phone.replace(digitCode(code), '');
    return [code, childPhone];
}

export function copy(text) {
    return import('electron').then(({clipboard}) => {
        clipboard.writeText(text);
    });
}

export function isPhoneNumber(item) {
    return item.phoneNumber && item.phoneNumber.length > 3;
}

export function emailOrPhoneNumber(item) {
    if (isPhoneNumber(item)) {
        return lookupCode(item.phoneNumber).join(' ');
    } else {
        return dataUtil.showEmailAndAccount(item.email);
    }
}

export const groupAvatar = getGroupAvatar;

export function setSettingsData(data) {
    let newSettings = {
        ...store.state.setting,
        ...data,
        spaceId: store.state.spaceCollection.currentSpaceId,
        hid: store.state.userInfo.hid,
        hostId: store.state.userInfo.hid
    };
    // console.log('setting', store.state.setting)
    delete newSettings.allSpaces;
    delete newSettings.receiptsSwitch;
    store.dispatch('setting/upgrade', newSettings);
}

// Same as one of `mangePeerList` function
// Optimization item
// 1 Remove repeated requests
// 2 Repeated quickly calls will not cause a large number of requests
function createTakePeerList() {
    let tempstate = {};
    return async function (spaceId, hid, realFn, isNeedServe) {
        let key = spaceId + hid;
        if (tempstate[key]) {
            let result = await tempstate[key];
            return result[0];
        } else {
            tempstate[key] = realFn(spaceId, [hid], 0, isNeedServe);
            let result = await tempstate[key];
            delete tempstate[key];
            return result[0];
        }
    };
}
export const takePeerList = createTakePeerList();

export function unblockThisPerson(Peer, Caught, hid) {
    MessageBox.confirm('This person was blocked by you. Do you want to unblock this person?', 'Unblock This Person', {
        confirmButtonText: i18n.t('right_content_panel.unblock_button'),
        cancelButtonText: i18n.t('settings_groupLogic.cancelButtonText'),
        customClass: 'myconfirm blueBtn',
        showClose: false
    }).then(() => {
        Caught.errorWith(async () => {
            await Peer.updateBlock(hid, false, 0);
            Message({
                message: i18n.t('unblockedSuccess'),
                type: 'success'
            });
        });
    });
}

export function fix(fn) {
    return function (...args) {
        return fn(fix(fn))(...args);
    };
}

// 多行时右键菜单仅留Copy
export function multiLineMenus(menus, info) {
    let selectedText = window.getSelection() ? window.getSelection().toString() : '';
    if (!selectedText || !info.plainMsg.m.body || info?.plainMsg?.m?.body?.indexOf(selectedText.toString()) >= 0) {
        return menus;
    } else {
        return menus.filter(a => a.label == i18n.t('right_content_panel.right_click_panel_copy'));
    }
}

// 将用户数组转化为对象，hid为key
export function getMemberObjByArr(memberList) {
    let result = {};
    memberList.forEach(item => {
        result[item.hid] = item;
    });
    return result;
}

// 根据protraitMtime格式化hidlist参数
export function formatHidArgByProtraitMtime(hidList, members, spaceId = defaultSpaceId(), isEditContact) {
    let hids = [];
    let noNeedFetchHidList = [];
    let noProtraitMtimeList = [];

    // console.log('formatHidArgByProtraitMtime', hidList, members);
    // devLog.log('formatHidArgByProtraitMtime', hidList, members);

    hidList.forEach(hid => {
        if (members[hid]) {
            if (!members[hid].protraitMtime) {
                noProtraitMtimeList.push(hid);
            } else {
                const peerData = store.state.peerCollection[enCodeSpaceHid(hid, spaceId)];

                // console.log('peerData', peerData);
                if (
                    !peerData ||
                    !peerData.protraitMtime ||
                    (peerData.protraitMtime && !peerData.portraitPath) ||
                    Number(peerData.protraitMtime) < Number(members[hid].protraitMtime) ||
                    isEditContact
                ) {
                    hids.push(hid);
                } else {
                    noNeedFetchHidList.push(hid);
                }
            }
        } else {
            hids.push(hid);
        }
    });
    // console.log(
    //     '🚀 ~ file: Function.js ~ line 1173 ~ formatHidArgByProtraitMtime',
    //     hidList,
    //     hids,
    //     noNeedFetchHidList,
    //     noProtraitMtimeList,
    //     members
    // );
    return {
        hids,
        noNeedFetchHidList,
        noProtraitMtimeList
    };
}

// 避免段时间内重复请求，通过key来复用相同请求
function avoidDuplicateMethod() {
    let fnCache = {};
    return async function (cachKey, realFn, ...requestParms) {
        let key = cachKey;
        let result;
        try {
            if (fnCache[key]) {
                result = await fnCache[key];
            } else {
                fnCache[key] = realFn(...requestParms);
                result = await fnCache[key];
            }
        } catch (error) {
            delete fnCache[key];
            return error;
        } finally {
            delete fnCache[key];
        }
        return result;
    };
}
export const doAvoidDuplicateMethod = avoidDuplicateMethod();

// 判断当前空间有没有过期
export function checkCurrentSpaceExpire() {
    if (envConfig.isPrivated) {
        return;
    }
    if (appdataStorage.loginStatus === 'LOGINFINISH') {
        const currentSpace = store.getters['spaceCollection/getSpaceInfo'](store.state.spaceCollection.currentSpaceId);
        if (currentSpace && currentSpace.expiration <= getTimestamp()) {
            devLog.log('checkCurrentSpaceExpire', currentSpace);
            throw new PlainException(i18n.t('spaceExpired'));
        }
    }
    return true;
}

export function generalReadyInfo(info) {
    return {
        ...info,
        osType: 3,
        appVersion: version,
        dev: 2,
        appId: `1001`
    };
}

export function verifyURL(str) {
    const isAppPreview = store.getters['spaceLimit/isAppPreview'];

    if (envConfig.isPrivated && !isAppPreview) {
        let result = '';
        store.state.spaceCollection.urlWhiteList?.some(rule => {
            try {
                const ruleProtocol = rule.type === 1 ? 'https' : 'http';
                const link = str.startsWith('http') ? str : ruleProtocol + '://' + str;

                const regex = new RegExp(`^(${ruleProtocol}):\/\/([a-zA-Z0-9-]+\\.)?${rule.address}(:\\d+)?(\\/.*)?$`);
                if (regex.test(link)) {
                    result = link;
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            }
        });
        return {
            url: str,
            safe: result,
            inWebview: true
        };
    } else {
        return {
            url: str,
            safe: true,
            inWebview: false
        };
    }
}

export function openLink(str) {
    const link = str.startsWith('http') ? str : 'https://' + str;
    const info = matchMeetingUrl(str);

    if (info) {
        if (info.isDomain || !envConfig.isPrivated) {
            new MeetingService({
                meetingEventType: joinMeetingEnum.Link_join
            }).linkJoinMeeting({
                url: link
            });
        } else if (envConfig.isPrivated && !/\matrx.(io|tech)/.test(link)) {
            Bus.$emit('open-meeting-error');
        } else {
            window.open(link);
        }
    } else {
        const res = verifyURL(link);

        if (!res.inWebview) {
            handleLiveLink(link, res.inWebview);
        } else if (res.safe) {
            handleLiveLink(res.url, res.inWebview);
        } else {
            ipcRenderer.send('open-webview', {safe: false, message: i18n.t('url_not_safe')});
        }
    }
}

/**
 * Check and process the live streaming link
 * @param {string} link - The live streaming link
 */
export async function handleLiveLink(link, inWebview) {
    try {
        ipcRenderer.sendSync('CST_SDK_PARSE_LIVE_LINK_SYNC', {link});

        /** @type {number} - Indicates whether the live link is valid. 0: Success; -1: Failure */
        const result = await parseLiveLinkRes();
        console.log('result:', result);

        if (result === 0) {
            return;
        }
        if (inWebview) {
            ipcRenderer.send('open-webview', {url: link, safe: true});
        } else {
            window.open(link);
        }
    } catch (error) {
        console.error('Failed to parse live link:', error);
    }
}

export async function parseLiveLinkRes(timeout = 3000) {
    return new Promise((resolve, reject) => {
        const listener = (e, data) => {
            if (typeof data !== 'object' || data === null || !('code' in data)) {
                // reject(new Error('Invalid response format'));
                resolve(-1);
                return;
            }
            resolve(data.code);
        };

        ipcRenderer.once('CST_SDK_PARSE_LIVE_LINK_RES', listener);

        setTimeout(() => {
            ipcRenderer.removeListener('CST_SDK_PARSE_LIVE_LINK_RES', listener);
            resolve(-1);
            // reject(new Error('Request timed out'));
        }, timeout);
    });
}

export function getLangText(data) {
    if (!data) {
        return '';
    }
    if (typeof data === 'string') {
        return data;
    }
    return data[i18n.locale];
}
export const forwordMerge = messageList => async (hid, needRepliedInfo) => {
    console.log('CombineChat forwordMerge', messageList, hid, needRepliedInfo);
    const mergedMessages = [];

    for (let i = 0; i < messageList.length; i++) {
        let message = messageList[i];
        let userInfos = store.getters['peerCollection/getPeerInfo'](message.plainMsg.hostId, defaultSpaceId(), true);
        console.log('CombineChat userInfos', userInfos);

        mergedMessages.push({
            uuid: message.uuid,
            type: message.plainMsg.MIMETYPE,
            stime: message.plainMsg.stime,
            senderUid: +dataUtil.hidToNumber(message.plainMsg.hostId),
            senderName: userInfos?.name,
            // robotId: message.robotId,
            // robotName: message.robotName,
            body: message.plainMsg.body
        });
    }

    if (mergedMessages.length === 0) {
        Message({
            type: 'warning',
            message: i18n.t('message_delete_sorry')
        });
        throw i18n.t('message_delete_sorry');
    }

    let msg = new MsgTemplate().getMsgTemplate(hid);

    msg.m.stime = getTimestamp();
    msg.m.MIMETYPE = 'text/combine';
    msg.m.meta = {
        previewContent: mergedMessages,
        combineId: uuidv1()
    };
    return sendMsg(msg);
};
